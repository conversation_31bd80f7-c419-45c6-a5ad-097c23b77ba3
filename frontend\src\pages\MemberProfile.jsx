import { useEffect, useState } from "react";
import ProductGrid from "../components/Products/ProductGrid";
import ReviewSection from "../components/Profile/ReviewSection";
import {
    FaMapMarkerAlt,
    FaClock,
    FaUserFriends,
    FaStar,
} from "react-icons/fa";
import { LuPencil } from "react-icons/lu";
import { useNavigate } from "react-router-dom";
import { getProfile } from "../api/AuthService";
import LoadingSpinner from "../components/common/LoadingSpinner";
import { EmptyState } from "../components/common/Animations";
import { getProduct } from "../api/ProductService";
import { CheckCircle2 } from "lucide-react";
import { formatDistanceToNowStrict } from 'date-fns';
import { useTranslation } from "react-i18next";
import MemberProfileSkeleton from "../components/Skeleton/MemberProfileSkeleton";

export default function MemberProfile() {
    const [activeTab, setActiveTab] = useState("listings");
    const [profileData, setProfileData] = useState(null); // store user profile
    const baseURL = import.meta.env.VITE_API_BASE_URL.replace(/\/$/, '');
    const [isLoadingProducts, setIsLoadingProducts] = useState(true);
    const { t } = useTranslation();
    const navigate = useNavigate();
    const rating = 4;
    const [product, setProduct] = useState([])
    const [user, setUser] = useState("")
    const tabs = ["listings", "reviews"];
    const [page, setPage] = useState(1);
    const [limit] = useState(10); // You can change this if needed
    const [totalPages, setTotalPages] = useState(0);
    const [isLoadingMore, setIsLoadingMore] = useState(false);

    useEffect(() => {
        getProfile().then((res) => {
            if (res?.success) {
                setProfileData(res?.data?.data);
            }
        });
    }, []);

    // useEffect(() => {
    //     if (profileData?.id) {
    //         setIsLoadingProducts(true); // Start loading
    //         getProduct()
    //             .then((res) => {
    //                 const items = res?.data?.data?.items || [];
    //                 setProduct(items);
    //                 setUser(profileData);
    //             })
    //             .catch((err) => {
    //                 console.log(err, "err");
    //             })
    //             .finally(() => {
    //                 setIsLoadingProducts(false); // Stop loading
    //             });
    //     }
    // }, [profileData?.id]);

    const fetchMyProducts = async (pageNumber = 1, append = false) => {
        const query = { page: pageNumber, limit };

        const setLoading = append ? setIsLoadingMore : setIsLoadingProducts;
        setLoading(true);

        try {
            const res = await getProduct(query);
            const items = res?.data?.data?.items || [];

            setProduct(prev => append ? [...prev, ...items] : items);
            setTotalPages(res?.data?.data?.totalPages || 0);
            setUser(profileData);
        } catch (err) {
            console.error("Fetch my products failed:", err);
        } finally {
            setLoading(false);
        }
    };

    useEffect(() => {
        if (!profileData?.id) return;
        setPage(1);
        fetchMyProducts(1, false);
    }, [profileData?.id]);

    const handleLoadMore = () => {
        const nextPage = page + 1;
        setPage(nextPage);
        fetchMyProducts(nextPage, true); // Append more items
    };

    if (!profileData) {
        return <MemberProfileSkeleton />;
    }

    return (
        <div className="container mx-auto p-4">
            {/* Header */}
            <div className="flex flex-col md:flex-row items-center md:items-start gap-8">
                <img
                    src={profileData.profile ? `${baseURL}${profileData.profile}` : "https://cdn-icons-png.flaticon.com/512/149/149071.png"}
                    alt="Profile"
                    className="w-44 h-44 rounded-full object-cover border-4 border-white shadow-md"
                />

                <div className="flex-1 space-y-3">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between w-full gap-2">
                        <h2 className="text-2xl font-semibold text-gray-800">
                            {profileData?.firstName} {profileData?.lastName}
                        </h2>
                        <button
                            className="flex items-center gap-2 px-5 py-2 hover:bg-gray-100 text-teal-700 rounded-md font-semibold border border-teal-600"
                            onClick={() => navigate("/settings/profile_details")}
                        >
                            <LuPencil className="w-4 h-4" />
                            {t("edit_profile")}
                        </button>
                    </div>

                    {/* Star Rating */}
                    <div className="flex items-center text-yellow-500 text-2xl">
                        {[...Array(5)].map((_, i) => (
                            <FaStar
                                key={i}
                                className={`text-xl ${i < rating ? "text-yellow-500" : "text-gray-300"}`}
                            />
                        ))}
                        <span className="text-gray-700 text-lg ml-2">{rating} {t("reviews")}</span>
                    </div>
                    {/* <div className="flex items-center gap-3 text-md text-gray-700">
                        <FaBoxOpen className="text-teal-700 text-2xl" />
                        <span className="text-teal-700 font-semibold">Frequent Uploads</span>
                        <span>· Regularly lists 5 or more items</span>
                    </div> */}

                    {/* Location, Last seen, Followers */}
                    <div className="flex items-start gap-12 flex-wrap text-md text-gray-600">
                        {/* Left Side - About Section */}
                        <div className="space-y-3">
                            <span className="text-sm font-semibold text-gray-700">{t("about")}:</span>
                            {profileData?.country &&
                                <div className="flex items-center gap-2">
                                    <FaMapMarkerAlt className="text-xl" />
                                    <span>{profileData?.cityShow && `${profileData.city}, `}{profileData?.country}</span>
                                </div>}
                            <div className="flex items-center gap-2">
                                <FaClock className="text-xl" />
                                <span>{t("last_seen")} {"  "}{profileData?.lastLoginAt ? formatDistanceToNowStrict(new Date(profileData?.lastLoginAt), { addSuffix: true }) : "1 Hours ago"}</span>
                            </div>
                            <div className="flex items-center gap-2">
                                <FaUserFriends className="text-xl" />
                                <span>
                                    <span className="font-semibold text-teal-600 underline cursor-pointer" onClick={() => navigate(`/followers/${profileData.id}`)}>
                                        {profileData?.followers}
                                    </span>{" "}
                                    {t("followers")},{" "}
                                    <span className="cursor-pointer font-semibold text-teal-600 underline" onClick={() => navigate(`/following/${profileData.id}`)}>
                                        {profileData?.following}
                                    </span>{" "}
                                    {t("following")}
                                </span>
                            </div>
                        </div>

                        {/* Right Side - Verified Info */}
                        <div className="space-y-3">
                            <span className="text-sm font-semibold text-gray-700">{t("verified_info")}:</span>
                            {profileData?.email && (
                                <div className="flex items-center gap-2">
                                    <CheckCircle2 />
                                    <span className=" ">{t("email")}</span>
                                </div>)}
                            {profileData?.loginWithGoogle && (<div className="flex items-center gap-2">
                                <CheckCircle2 />
                                <span className=" ">{t("google")} </span>
                            </div>)}
                            {profileData?.loginWithFacebook && (<div className="flex items-center gap-2">
                                <CheckCircle2 />
                                <span className=" ">{t("facebook")} </span>
                            </div>)}
                        </div>
                    </div>
                    <span className="flex items-start gap-12 flex-wrap text-md text-gray-600">{profileData?.about}</span>
                </div>
            </div>

            {/* Tabs */}
            <div className="mt-6 border-b flex gap-6 text-sm font-medium text-gray-600">
                {tabs.map((tab) => (
                    <button
                        key={tab}
                        onClick={() => setActiveTab(tab)}
                        className={`pb-2 ${activeTab === tab
                            ? "border-b-2 border-teal-700 text-teal-700"
                            : "hover:text-teal-700"
                            }`}
                    >
                        {t(tab)}
                    </button>
                ))}
            </div>

            {/* Tab Content */}
            <div className="mt-4 min-h-[600px]">
                {activeTab === "listings" ? (
                    <div className="flex-grow">
                        {isLoadingProducts ? (
                            <LoadingSpinner fullScreen={false} />
                        ) : product && product.length > 0 ? (
                            <>
                                <ProductGrid products={product} user={user} />

                                {/* Load More Button */}
                                {page < totalPages && (
                                    <div className="flex justify-center mt-6">
                                        <button
                                            onClick={handleLoadMore}
                                            className="px-6 py-2 bg-teal-600 text-white rounded hover:bg-teal-700 disabled:opacity-50"
                                            disabled={isLoadingMore}
                                        >
                                            {isLoadingMore ? 'Loading...' : 'Load More'}
                                        </button>
                                    </div>
                                )}
                            </>
                        ) : (
                            <EmptyState />
                        )}
                    </div>
                ) : (
                    <div className="text-gray-600">
                        <ReviewSection userProfile={profileData} />
                    </div>
                )}
            </div>
        </div>
    );
}
