import React, { useState } from 'react';
import ChatRoom from './components/ChatRoom';
import ChatList from './components/ChatList';
import ProductChatButton from './components/ProductChatButton';
import './App.css';

function App() {
  // Demo user and product IDs
  const currentUserId = '6836b8bc2caa73f5098bb68d';
  const productId = '68497b9d588aec0773fc41ff';

  const [activeView, setActiveView] = useState('demo'); // 'demo', 'chatList', 'productChat'
  const [selectedChat, setSelectedChat] = useState(null);

  const handleChatSelect = (chat) => {
    setSelectedChat(chat);
    setActiveView('chatRoom');
  };

  const renderDemo = () => (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <h1>SOUQ Chat System Demo</h1>
      <p>This demo shows the complete chat functionality for your marketplace.</p>

      <div style={{ display: 'grid', gap: '20px', marginTop: '30px' }}>
        {/* Product Chat Demo */}
        <div style={{
          padding: '20px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          backgroundColor: '#f8f9fa'
        }}>
          <h3>1. Product Chat Button</h3>
          <p>This button would appear on product detail pages. Click it to start a chat with the seller.</p>
          <div style={{ maxWidth: '300px' }}>
            <ProductChatButton
              productId={productId}
              currentUserId={currentUserId}
              buttonText="Message seller"
            />
          </div>
        </div>

        {/* Chat List Demo */}
        <div style={{
          padding: '20px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          backgroundColor: '#f8f9fa'
        }}>
          <h3>2. Chat List View</h3>
          <p>This shows all user conversations. Click to view the chat list.</p>
          <button
            onClick={() => setActiveView('chatList')}
            style={{
              padding: '12px 24px',
              backgroundColor: '#007bff',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            View All Chats
          </button>
        </div>

        {/* Direct Chat Demo */}
        <div style={{
          padding: '20px',
          border: '1px solid #ddd',
          borderRadius: '8px',
          backgroundColor: '#f8f9fa'
        }}>
          <h3>3. Direct Chat Room</h3>
          <p>This opens a chat room directly for a specific product.</p>
          <button
            onClick={() => setActiveView('productChat')}
            style={{
              padding: '12px 24px',
              backgroundColor: '#28a745',
              color: 'white',
              border: 'none',
              borderRadius: '6px',
              cursor: 'pointer'
            }}
          >
            Open Product Chat
          </button>
        </div>
      </div>

      <div style={{ marginTop: '40px', padding: '20px', backgroundColor: '#e9ecef', borderRadius: '8px' }}>
        <h3>Features Included:</h3>
        <ul style={{ lineHeight: '1.6' }}>
          <li>✅ Real-time messaging with WebSocket</li>
          <li>✅ Product-based chat rooms</li>
          <li>✅ Message history and pagination</li>
          <li>✅ Typing indicators</li>
          <li>✅ Message seen/delivered status</li>
          <li>✅ Online/offline status</li>
          <li>✅ Unread message counts</li>
          <li>✅ User authentication</li>
          <li>✅ Responsive design</li>
          <li>✅ Chat list with last message preview</li>
        </ul>
      </div>
    </div>
  );

  const renderChatList = () => (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => setActiveView('demo')}
          style={{
            padding: '8px 16px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          ← Back to Demo
        </button>
      </div>
      <ChatList
        onChatSelect={handleChatSelect}
        currentUserId={currentUserId}
      />
    </div>
  );

  const renderProductChat = () => (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => setActiveView('demo')}
          style={{
            padding: '8px 16px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          ← Back to Demo
        </button>
      </div>
      <ChatRoom
        productId={productId}
        currentUserId={currentUserId}
      />
    </div>
  );

  const renderChatRoom = () => (
    <div style={{ padding: '20px', maxWidth: '800px', margin: '0 auto' }}>
      <div style={{ marginBottom: '20px' }}>
        <button
          onClick={() => setActiveView('chatList')}
          style={{
            padding: '8px 16px',
            backgroundColor: '#6c757d',
            color: 'white',
            border: 'none',
            borderRadius: '4px',
            cursor: 'pointer'
          }}
        >
          ← Back to Chat List
        </button>
      </div>
      {selectedChat && (
        <ChatRoom
          productId={selectedChat.product.id}
          currentUserId={currentUserId}
        />
      )}
    </div>
  );

  return (
    <div className="App">
      {activeView === 'demo' && renderDemo()}
      {activeView === 'chatList' && renderChatList()}
      {activeView === 'productChat' && renderProductChat()}
      {activeView === 'chatRoom' && renderChatRoom()}
    </div>
  );
}

export default App;