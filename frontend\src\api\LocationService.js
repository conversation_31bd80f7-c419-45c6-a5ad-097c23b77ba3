import axios from 'axios';

const API_BASE_URL = 'http://localhost:5000/api/user/location';

// Create axios instance with default config
const locationAPI = axios.create({
  baseURL: API_BASE_URL,
  timeout: 10000,
});

// Add request interceptor to include auth token
locationAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

// Add response interceptor for error handling
locationAPI.interceptors.response.use(
  (response) => response,
  (error) => {
    console.error('Location API Error:', error);
    return Promise.reject(error);
  }
);

// Country API functions
export const getCountries = async () => {
  try {
    console.log('📍 Fetching countries...');
    const response = await locationAPI.get('/countries');
    console.log('✅ Countries fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error fetching countries:', error);
    throw error;
  }
};

export const getCountryById = async (countryId) => {
  try {
    console.log('📍 Fetching country by ID:', countryId);
    const response = await locationAPI.get(`/countries/${countryId}`);
    console.log('✅ Country fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error fetching country:', error);
    throw error;
  }
};

export const getCountryByCode = async (countryCode) => {
  try {
    console.log('📍 Fetching country by code:', countryCode);
    const response = await locationAPI.get(`/countries/code/${countryCode}`);
    console.log('✅ Country fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error fetching country by code:', error);
    throw error;
  }
};

export const searchCountries = async (query) => {
  try {
    console.log('📍 Searching countries with query:', query);
    const response = await locationAPI.get('/countries/search', {
      params: { q: query }
    });
    console.log('✅ Countries search completed:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error searching countries:', error);
    throw error;
  }
};

// City API functions
export const getCitiesByCountry = async (countryId) => {
  try {
    console.log('🏙️ Fetching cities for country ID:', countryId);
    const response = await locationAPI.get(`/cities/country/${countryId}`);
    console.log('✅ Cities fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error fetching cities by country:', error);
    throw error;
  }
};

export const getCitiesByCountryCode = async (countryCode) => {
  try {
    console.log('🏙️ Fetching cities for country code:', countryCode);
    const response = await locationAPI.get(`/cities/country-code/${countryCode}`);
    console.log('✅ Cities fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error fetching cities by country code:', error);
    throw error;
  }
};

export const searchCities = async (query, countryId = null) => {
  try {
    console.log('🏙️ Searching cities with query:', query, 'Country ID:', countryId);
    const params = { q: query };
    if (countryId) {
      params.countryId = countryId;
    }
    
    const response = await locationAPI.get('/cities/search', { params });
    console.log('✅ Cities search completed:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error searching cities:', error);
    throw error;
  }
};

export const getCityById = async (cityId) => {
  try {
    console.log('🏙️ Fetching city by ID:', cityId);
    const response = await locationAPI.get(`/cities/${cityId}`);
    console.log('✅ City fetched:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Error fetching city:', error);
    throw error;
  }
};

// Helper functions
export const formatCountryDisplay = (country) => {
  if (!country) return '';
  return country.flag ? ` ${country.name}` : country.name;
};

export const formatCityDisplay = (city) => {
  if (!city) return '';
  return city.state ? `${city.name}, ${city.state}` : city.name;
};

export const formatFullLocationDisplay = (city, country) => {
  if (!city || !country) return '';
  const cityDisplay = formatCityDisplay(city);
  const countryDisplay = formatCountryDisplay(country);
  return `${cityDisplay}, ${countryDisplay}`;
};

// Default export with all functions
const LocationService = {
  // Countries
  getCountries,
  getCountryById,
  getCountryByCode,
  searchCountries,
  
  // Cities
  getCitiesByCountry,
  getCitiesByCountryCode,
  searchCities,
  getCityById,
  
  // Helpers
  formatCountryDisplay,
  formatCityDisplay,
  formatFullLocationDisplay
};

export default LocationService;
