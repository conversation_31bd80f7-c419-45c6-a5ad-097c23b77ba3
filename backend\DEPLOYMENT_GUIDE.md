# SOUQ Escrow System Deployment Guide

## 🚀 Production Deployment Steps

### 1. Environment Configuration

#### Backend Environment Variables (.env)
```env
# Database
MONGODB_URI=mongodb://localhost:27017/souq_production
# or for MongoDB Atlas:
# MONGODB_URI=mongodb+srv://username:<EMAIL>/souq_production

# Server Configuration
PORT=5000
NODE_ENV=production
BASE_URL=https://your-domain.com

# JWT Configuration
JWT_SECRET=your-super-secure-jwt-secret-key-here
JWT_EXPIRES_IN=7d

# PayTabs Configuration
PAYTABS_PROFILE_ID=your_production_profile_id
PAYTABS_SERVER_KEY=your_production_server_key

# Stripe Configuration
STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key
STRIPE_SECRET_KEY=sk_live_your_secret_key
STRIPE_WEBHOOK_SECRET=whsec_your_webhook_secret

# PayPal Configuration
PAYPAL_CLIENT_ID=your_production_client_id
PAYPAL_CLIENT_SECRET=your_production_client_secret
PAYPAL_ENVIRONMENT=live

# PayFort Configuration (when implemented)
PAYFORT_ACCESS_CODE=your_access_code
PAYFORT_MERCHANT_ID=your_merchant_id
PAYFORT_SHA_REQUEST=your_sha_request_phrase
PAYFORT_SHA_RESPONSE=your_sha_response_phrase
PAYFORT_ENVIRONMENT=production

# Checkout.com Configuration (when implemented)
CHECKOUT_PUBLIC_KEY=pk_live_your_public_key
CHECKOUT_SECRET_KEY=sk_live_your_secret_key
CHECKOUT_ENVIRONMENT=live

# Exchange Rate API (optional)
EXCHANGE_RATE_API_KEY=your_exchange_rate_api_key

# Email Configuration (for notifications)
SMTP_HOST=smtp.your-provider.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASS=your-email-password

# File Upload Configuration
UPLOAD_PATH=/var/www/uploads
MAX_FILE_SIZE=10485760

# Security
CORS_ORIGIN=https://your-frontend-domain.com
RATE_LIMIT_WINDOW=15
RATE_LIMIT_MAX=100
```

#### Frontend Environment Variables (.env)
```env
VITE_API_BASE_URL=https://your-api-domain.com
VITE_STRIPE_PUBLISHABLE_KEY=pk_live_your_publishable_key
VITE_PAYPAL_CLIENT_ID=your_production_client_id
VITE_ENVIRONMENT=production
```

### 2. Database Setup

#### MongoDB Production Setup
```bash
# For self-hosted MongoDB
sudo systemctl start mongod
sudo systemctl enable mongod

# Create production database and user
mongo
use souq_production
db.createUser({
  user: "souq_user",
  pwd: "secure_password",
  roles: [{ role: "readWrite", db: "souq_production" }]
})

# For MongoDB Atlas
# 1. Create cluster on MongoDB Atlas
# 2. Configure network access (whitelist your server IP)
# 3. Create database user
# 4. Get connection string
```

#### Initialize Escrow System
```bash
cd souq-backend
NODE_ENV=production node scripts/initializeEscrowSystem.js
```

### 3. Payment Gateway Configuration

#### PayTabs Setup
1. Login to PayTabs merchant dashboard
2. Navigate to Developer Settings
3. Configure webhook URL: `https://your-domain.com/api/user/escrow/webhook/paytabs`
4. Copy Profile ID and Server Key
5. Test with small transaction

#### Stripe Setup
1. Login to Stripe Dashboard
2. Go to Developers > Webhooks
3. Add endpoint: `https://your-domain.com/api/user/escrow/webhook/stripe`
4. Select events: `payment_intent.succeeded`, `payment_intent.payment_failed`
5. Copy webhook secret
6. Enable live mode

#### PayPal Setup
1. Login to PayPal Developer Dashboard
2. Create live application
3. Configure webhook URL: `https://your-domain.com/api/user/escrow/webhook/paypal`
4. Subscribe to events: `PAYMENT.CAPTURE.COMPLETED`, `PAYMENT.CAPTURE.DENIED`
5. Copy Client ID and Secret

### 4. Server Deployment

#### Using PM2 (Recommended)
```bash
# Install PM2 globally
npm install -g pm2

# Create ecosystem file
cat > ecosystem.config.js << EOF
module.exports = {
  apps: [{
    name: 'souq-backend',
    script: 'userApp.js',
    instances: 'max',
    exec_mode: 'cluster',
    env: {
      NODE_ENV: 'development'
    },
    env_production: {
      NODE_ENV: 'production',
      PORT: 5000
    },
    error_file: './logs/err.log',
    out_file: './logs/out.log',
    log_file: './logs/combined.log',
    time: true
  }]
}
EOF

# Start application
pm2 start ecosystem.config.js --env production
pm2 save
pm2 startup
```

#### Using Docker
```dockerfile
# Dockerfile
FROM node:18-alpine

WORKDIR /app

COPY package*.json ./
RUN npm ci --only=production

COPY . .

EXPOSE 5000

CMD ["node", "userApp.js"]
```

```yaml
# docker-compose.yml
version: '3.8'
services:
  souq-backend:
    build: .
    ports:
      - "5000:5000"
    environment:
      - NODE_ENV=production
    env_file:
      - .env
    depends_on:
      - mongodb
    restart: unless-stopped

  mongodb:
    image: mongo:6
    ports:
      - "27017:27017"
    volumes:
      - mongodb_data:/data/db
    restart: unless-stopped

volumes:
  mongodb_data:
```

### 5. Frontend Deployment

#### Build for Production
```bash
cd souq-frontend
npm run build

# Deploy to static hosting (Netlify, Vercel, etc.)
# Or serve with nginx
```

#### Nginx Configuration
```nginx
server {
    listen 80;
    server_name your-domain.com;
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    server_name your-domain.com;

    ssl_certificate /path/to/certificate.crt;
    ssl_certificate_key /path/to/private.key;

    # Frontend
    location / {
        root /var/www/souq-frontend/dist;
        try_files $uri $uri/ /index.html;
    }

    # API Proxy
    location /api/ {
        proxy_pass http://localhost:5000;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
    }

    # File uploads
    location /uploads/ {
        root /var/www;
        expires 1y;
        add_header Cache-Control "public, immutable";
    }
}
```

### 6. SSL Certificate Setup

#### Using Let's Encrypt (Certbot)
```bash
# Install certbot
sudo apt install certbot python3-certbot-nginx

# Obtain certificate
sudo certbot --nginx -d your-domain.com

# Auto-renewal
sudo crontab -e
# Add: 0 12 * * * /usr/bin/certbot renew --quiet
```

### 7. Monitoring and Logging

#### Setup Logging
```bash
# Create log directories
mkdir -p /var/log/souq
chown www-data:www-data /var/log/souq

# Logrotate configuration
cat > /etc/logrotate.d/souq << EOF
/var/log/souq/*.log {
    daily
    missingok
    rotate 52
    compress
    delaycompress
    notifempty
    create 644 www-data www-data
    postrotate
        pm2 reload souq-backend
    endscript
}
EOF
```

#### Setup Monitoring
```bash
# Install monitoring tools
npm install -g pm2-logrotate
pm2 install pm2-server-monit

# Setup health checks
cat > healthcheck.js << EOF
const http = require('http');

const options = {
  hostname: 'localhost',
  port: 5000,
  path: '/api/health',
  method: 'GET'
};

const req = http.request(options, (res) => {
  if (res.statusCode === 200) {
    console.log('Health check passed');
    process.exit(0);
  } else {
    console.log('Health check failed');
    process.exit(1);
  }
});

req.on('error', (e) => {
  console.log('Health check error:', e.message);
  process.exit(1);
});

req.end();
EOF

# Add to crontab for monitoring
# */5 * * * * /usr/bin/node /path/to/healthcheck.js
```

### 8. Security Hardening

#### Firewall Configuration
```bash
# UFW setup
sudo ufw default deny incoming
sudo ufw default allow outgoing
sudo ufw allow ssh
sudo ufw allow 80
sudo ufw allow 443
sudo ufw enable
```

#### Security Headers
```nginx
# Add to nginx server block
add_header X-Frame-Options "SAMEORIGIN" always;
add_header X-XSS-Protection "1; mode=block" always;
add_header X-Content-Type-Options "nosniff" always;
add_header Referrer-Policy "no-referrer-when-downgrade" always;
add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
```

### 9. Backup Strategy

#### Database Backup
```bash
#!/bin/bash
# backup-db.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/mongodb"
mkdir -p $BACKUP_DIR

mongodump --uri="$MONGODB_URI" --out="$BACKUP_DIR/backup_$DATE"
tar -czf "$BACKUP_DIR/backup_$DATE.tar.gz" -C "$BACKUP_DIR" "backup_$DATE"
rm -rf "$BACKUP_DIR/backup_$DATE"

# Keep only last 7 days
find $BACKUP_DIR -name "backup_*.tar.gz" -mtime +7 -delete

# Add to crontab: 0 2 * * * /path/to/backup-db.sh
```

#### File Backup
```bash
#!/bin/bash
# backup-files.sh
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_DIR="/var/backups/files"
mkdir -p $BACKUP_DIR

tar -czf "$BACKUP_DIR/uploads_$DATE.tar.gz" /var/www/uploads
find $BACKUP_DIR -name "uploads_*.tar.gz" -mtime +30 -delete
```

### 10. Performance Optimization

#### Redis Setup (for caching)
```bash
# Install Redis
sudo apt install redis-server

# Configure Redis
sudo nano /etc/redis/redis.conf
# Set: maxmemory 256mb
# Set: maxmemory-policy allkeys-lru

sudo systemctl restart redis
```

#### Database Indexing
```javascript
// Add to initialization script
db.escrowTransactions.createIndex({ "buyer": 1, "createdAt": -1 });
db.escrowTransactions.createIndex({ "seller": 1, "createdAt": -1 });
db.escrowTransactions.createIndex({ "status": 1, "createdAt": -1 });
db.escrowTransactions.createIndex({ "transactionId": 1 }, { unique: true });
db.escrowTransactions.createIndex({ "gatewayTransactionId": 1 });
```

### 11. Post-Deployment Verification

#### Deployment Checklist
- [ ] Application starts without errors
- [ ] Database connection successful
- [ ] Payment gateways responding
- [ ] Webhooks receiving correctly
- [ ] Currency rates updating
- [ ] SSL certificate valid
- [ ] All API endpoints accessible
- [ ] Frontend loading correctly
- [ ] File uploads working
- [ ] Email notifications sending
- [ ] Logs being written
- [ ] Monitoring active
- [ ] Backups running

#### Test Production Flow
1. Create test escrow transaction
2. Process test payment
3. Verify webhook processing
4. Test transaction lifecycle
5. Confirm admin dashboard access
6. Validate currency conversion
7. Test error handling

### 🎯 Production Ready!

Your SOUQ Escrow System is now deployed and ready for production use! 

**Remember to:**
- Monitor logs regularly
- Keep dependencies updated
- Review security regularly
- Monitor payment gateway status
- Backup data consistently
- Test new features thoroughly

**The system is production-ready with enterprise-grade features! 🚀**
