import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;

// Create axios instance for cards (using existing API)
const cardAPI = axios.create({
  baseURL: `${API_BASE_URL}/api/user/cards`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Create axios instance for bank accounts (new API)
const bankAPI = axios.create({
  baseURL: `${API_BASE_URL}/api/user/bank-accounts`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to card requests
cardAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    console.log('🔑 Card API - Token check:', token ? 'Token found' : 'No token found');
    console.log('🔗 Card API - Request URL:', config.url);

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('🚫 Card API - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Add auth token to bank requests
bankAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    console.log('🔑 Bank API - Token check:', token ? 'Token found' : 'No token found');
    console.log('🔗 Bank API - Request URL:', config.url);

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    console.error('🚫 Bank API - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for card API
cardAPI.interceptors.response.use(
  (response) => {
    console.log('✅ Card API - Response received:', response.status);
    return response;
  },
  (error) => {
    console.error('❌ Card API - Response error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

// Response interceptor for bank API
bankAPI.interceptors.response.use(
  (response) => {
    console.log('✅ Bank API - Response received:', response.status);
    return response;
  },
  (error) => {
    console.error('❌ Bank API - Response error:', error.response?.data || error.message);
    return Promise.reject(error);
  }
);

/**
 * Add a new card using existing card API
 * @param {Object} cardData - Card details
 * @returns {Promise<Object>} Card creation response
 */
export const addCard = async (cardData) => {
  try {
    console.log('🔄 Adding new card using existing API...');

    // Use the existing verify-and-save endpoint
    const requestData = {
      cardNumber: cardData.cardNumber,
      expiryMonth: cardData.expiryMonth,
      expiryYear: cardData.expiryYear,
      cvv: cardData.cvv,
      cardholderName: cardData.cardholderName,
      setAsDefault: cardData.isDefault || false,
      gateway: 'stripe'
    };

    console.log('Card request data:', requestData);
    const response = await cardAPI.post('/verify-and-save', requestData);
    console.log('✅ Card added successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to add card:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to add card' };
  }
};

/**
 * Add a new bank account
 * @param {Object} bankData - Bank account details
 * @returns {Promise<Object>} Bank account creation response
 */
export const addBankAccount = async (bankData) => {
  try {
    console.log('🔄 Adding new bank account...');

    const requestData = {
      accountHolderName: bankData.accountHolderName,
      accountNumber: bankData.accountNumber,
      routingNumber: bankData.routingNumber,
      accountType: bankData.accountType,
      bankName: bankData.bankName,
      billingAddress: bankData.billingAddress,
      setAsDefault: bankData.isDefault || false
    };

    console.log('Bank account request data:', requestData);
    const response = await bankAPI.post('/add', requestData);
    console.log('✅ Bank account added successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to add bank account:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to add bank account' };
  }
};

/**
 * Get user's cards using existing card API
 * @param {boolean} activeOnly - Whether to return only active cards (default: true)
 * @returns {Promise<Object>} Cards list
 */
export const getCards = async (activeOnly = true) => {
  try {
    console.log('🔄 Fetching cards using existing API...', { activeOnly });
    const response = await cardAPI.get('/', {
      params: { activeOnly }
    });
    console.log('✅ Cards fetched successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to fetch cards:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to fetch cards' };
  }
};

/**
 * Get user's bank accounts
 * @param {boolean} activeOnly - Whether to return only active accounts (default: true)
 * @returns {Promise<Object>} Bank accounts list
 */
export const getBankAccounts = async (activeOnly = true) => {
  try {
    console.log('🔄 Fetching bank accounts...', { activeOnly });
    const response = await bankAPI.get('/', {
      params: { activeOnly }
    });
    console.log('✅ Bank accounts fetched successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to fetch bank accounts:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to fetch bank accounts' };
  }
};

/**
 * Set card as default using existing card API
 * @param {string} cardId - Card ID
 * @returns {Promise<Object>} Update response
 */
export const setDefaultCard = async (cardId) => {
  try {
    console.log('🔄 Setting default card:', cardId);
    const response = await cardAPI.put(`/${cardId}/set-default`);
    console.log('✅ Default card set successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to set default card:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to set default card' };
  }
};

/**
 * Delete card using existing card API
 * @param {string} cardId - Card ID
 * @returns {Promise<Object>} Delete response
 */
export const deleteCard = async (cardId) => {
  try {
    console.log('🔄 Deleting card:', cardId);
    const response = await cardAPI.delete(`/${cardId}`);
    console.log('✅ Card deleted successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to delete card:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to delete card' };
  }
};

/**
 * Validate card number using Luhn algorithm
 * @param {string} cardNumber - Card number to validate
 * @returns {boolean} Is valid card number
 */
export const validateCardNumber = (cardNumber) => {
  // Remove spaces and non-digits
  const cleanNumber = cardNumber.replace(/\D/g, '');
  
  // Check if it's a valid length
  if (cleanNumber.length < 13 || cleanNumber.length > 19) {
    return false;
  }
  
  // Luhn algorithm
  let sum = 0;
  let isEven = false;
  
  for (let i = cleanNumber.length - 1; i >= 0; i--) {
    let digit = parseInt(cleanNumber.charAt(i));
    
    if (isEven) {
      digit *= 2;
      if (digit > 9) {
        digit -= 9;
      }
    }
    
    sum += digit;
    isEven = !isEven;
  }
  
  return sum % 10 === 0;
};

/**
 * Get card brand from card number
 * @param {string} cardNumber - Card number
 * @returns {string} Card brand
 */
export const getCardBrand = (cardNumber) => {
  const cleanNumber = cardNumber.replace(/\D/g, '');
  const firstDigit = cleanNumber.charAt(0);
  const firstTwoDigits = cleanNumber.substring(0, 2);
  const firstFourDigits = cleanNumber.substring(0, 4);

  if (firstDigit === '4') return 'visa';
  if (['51', '52', '53', '54', '55'].includes(firstTwoDigits)) return 'mastercard';
  if (['34', '37'].includes(firstTwoDigits)) return 'amex';
  if (firstFourDigits === '6011') return 'discover';
  return 'other';
};

/**
 * Format card number with spaces
 * @param {string} cardNumber - Card number
 * @returns {string} Formatted card number
 */
export const formatCardNumber = (cardNumber) => {
  const cleanNumber = cardNumber.replace(/\D/g, '');
  const groups = cleanNumber.match(/.{1,4}/g) || [];
  return groups.join(' ').substr(0, 19); // Max 16 digits + 3 spaces
};

/**
 * Format expiry date as MM/YY
 * @param {string} expiry - Expiry input
 * @returns {string} Formatted expiry
 */
export const formatExpiryDate = (expiry) => {
  const digits = expiry.replace(/\D/g, '');
  if (digits.length >= 3) {
    return digits.slice(0, 2) + '/' + digits.slice(2, 4);
  }
  return digits;
};

/**
 * Validate expiry date
 * @param {string} expiry - Expiry date in MM/YY format
 * @returns {boolean} Is valid expiry date
 */
export const validateExpiryDate = (expiry) => {
  const match = expiry.match(/^(0[1-9]|1[0-2])\/(\d{2})$/);
  if (!match) return false;
  
  const month = parseInt(match[1]);
  const year = parseInt('20' + match[2]);
  const now = new Date();
  const currentYear = now.getFullYear();
  const currentMonth = now.getMonth() + 1;
  
  if (year < currentYear) return false;
  if (year === currentYear && month < currentMonth) return false;
  
  return true;
};

/**
 * Set bank account as default
 * @param {string} accountId - Bank account ID
 * @returns {Promise<Object>} Update response
 */
export const setDefaultBankAccount = async (accountId) => {
  try {
    console.log('🔄 Setting default bank account:', accountId);
    const response = await bankAPI.put(`/${accountId}/set-default`);
    console.log('✅ Default bank account set successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to set default bank account:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to set default bank account' };
  }
};

/**
 * Delete bank account
 * @param {string} accountId - Bank account ID
 * @returns {Promise<Object>} Delete response
 */
export const deleteBankAccount = async (accountId) => {
  try {
    console.log('🔄 Deleting bank account:', accountId);
    const response = await bankAPI.delete(`/${accountId}`);
    console.log('✅ Bank account deleted successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('❌ Failed to delete bank account:', error.response?.data || error.message);
    throw error.response?.data || { success: false, error: 'Failed to delete bank account' };
  }
};

export default {
  addCard,
  addBankAccount,
  getCards,
  getBankAccounts,
  setDefaultCard,
  deleteCard,
  setDefaultBankAccount,
  deleteBankAccount,
  validateCardNumber,
  getCardBrand,
  formatCardNumber,
  formatExpiryDate,
  validateExpiryDate
};
