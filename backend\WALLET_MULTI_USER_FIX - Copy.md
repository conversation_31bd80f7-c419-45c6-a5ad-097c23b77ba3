# Wallet Multi-User Access Fix

## Problem
Users were experiencing a 500 error when accessing the wallet page:
```
"Failed to retrieve transaction history: E11000 duplicate key error collection: souq.wallets index: transactions.transactionId_1 dup key: { transactions.transactionId: null }"
```

**Root Cause:** MongoDB duplicate key error on `transactions.transactionId` field due to multiple transactions having `null` values, which violated the unique index.

## Solution Applied

### 1. Fixed Wallet Model Schema
**File:** `souq-backend/db/models/walletModel.js`

#### Removed Problematic Index
```javascript
// BEFORE: Caused duplicate key errors
walletSchema.index({ 'transactions.transactionId': 1 }, { sparse: true });

// AFTER: Removed to prevent null value conflicts
// walletSchema.index({ 'transactions.transactionId': 1 }, { sparse: true }); // Commented out
```

#### Enhanced Transaction ID Generation
```javascript
// BEFORE: Simple generation
walletTransactionSchema.pre('validate', function(next) {
  if (!this.transactionId) {
    this.transactionId = `WTX_${Date.now()}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`;
  }
  next();
});

// AFTER: More unique with user context
walletTransactionSchema.pre('validate', function(next) {
  if (!this.transactionId) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8).toUpperCase();
    const userPart = this.parent()?.user?.toString().slice(-6) || 'NOUSER';
    this.transactionId = `WTX_${timestamp}_${userPart}_${random}`;
  }
  next();
});
```

#### Improved addTransaction Method
```javascript
// Generate unique transaction ID if not provided
const transactionId = transactionData.transactionId || 
  `WTX_${Date.now()}_${this.user.toString().slice(-6)}_${Math.random().toString(36).substring(2, 8).toUpperCase()}`;

const transaction = {
  type,
  amount,
  currency,
  balanceAfter: newBalance,
  description,
  transactionId, // ✅ Always has a unique ID
  relatedTransaction,
  relatedEscrowTransaction,
  relatedProduct,
  metadata: metadata || {}
};
```

### 2. Enhanced Wallet Controller
**File:** `souq-backend/app/user/wallet/controllers/walletController.js`

#### Robust Wallet Creation
```javascript
exports.getWallet = async (req, res) => {
  // Try to find existing wallet first
  let wallet = await Wallet.findOne({ user: userId });
  
  if (!wallet) {
    try {
      wallet = await Wallet.findOrCreateWallet(userId);
    } catch (createError) {
      // Handle duplicate key errors gracefully
      if (createError.message.includes('E11000')) {
        wallet = await Wallet.findOne({ user: userId });
        if (!wallet) {
          throw new Error('Failed to create or find wallet after duplicate key error');
        }
      } else {
        throw createError;
      }
    }
  }
  // ... rest of function
};
```

#### Improved Transaction History
```javascript
exports.getTransactionHistory = async (req, res) => {
  // Find wallet without complex population first
  let wallet = await Wallet.findOne({ user: userId });

  if (!wallet) {
    try {
      wallet = await Wallet.findOrCreateWallet(userId);
    } catch (createError) {
      // Handle duplicate key errors
      if (createError.message.includes('E11000')) {
        wallet = await Wallet.findOne({ user: userId });
      }
    }
  }

  // Safely populate wallet data
  try {
    wallet = await Wallet.findOne({ user: userId })
      .populate('user', 'firstName lastName email')
      .populate('transactions.relatedProduct', 'title price product_photos');
  } catch (populateError) {
    // Continue with basic wallet data if population fails
    console.log('⚠️ Continuing with basic wallet data without population');
  }
  // ... rest of function
};
```

### 3. Database Cleanup Function
**File:** `souq-backend/app/user/wallet/controllers/walletController.js`

Added a cleanup function to fix existing duplicate transaction IDs:

```javascript
exports.fixWalletDuplicateKeys = async (req, res) => {
  // Find all wallets and fix duplicate/null transaction IDs
  const wallets = await Wallet.find({});
  
  for (const wallet of wallets) {
    const seenTransactionIds = new Set();
    
    for (let i = 0; i < wallet.transactions.length; i++) {
      const transaction = wallet.transactions[i];
      
      // Fix null or duplicate transaction IDs
      if (!transaction.transactionId || seenTransactionIds.has(transaction.transactionId)) {
        const timestamp = transaction.createdAt ? transaction.createdAt.getTime() : Date.now();
        const userPart = wallet.user.toString().slice(-6);
        const random = Math.random().toString(36).substring(2, 8).toUpperCase();
        const newId = `WTX_${timestamp}_${userPart}_${random}`;
        
        wallet.transactions[i].transactionId = newId;
        seenTransactionIds.add(newId);
      }
    }
    
    await wallet.save();
  }
};
```

**Route:** `POST /api/user/wallet/fix-duplicate-keys`

## Multi-User Wallet Access

### Before Fix
- ❌ Only one user could access wallet
- ❌ Other users got 500 errors
- ❌ Duplicate key errors prevented wallet creation
- ❌ Transaction history failed to load

### After Fix
- ✅ Every user can access their wallet
- ✅ Automatic wallet creation for new users
- ✅ Unique transaction IDs prevent conflicts
- ✅ Graceful error handling for edge cases
- ✅ Database cleanup function available

## Testing the Fix

### 1. Run Database Cleanup
```bash
# Call the cleanup API endpoint
curl -X POST http://localhost:5000/api/user/wallet/fix-duplicate-keys \
  -H "Authorization: Bearer YOUR_ACCESS_TOKEN"
```

### 2. Test Multi-User Access
1. **Login with User A** → Go to wallet page → Should work ✅
2. **Login with User B** → Go to wallet page → Should work ✅
3. **Login with User C** → Go to wallet page → Should work ✅

### 3. Verify Wallet Creation
Each user should have:
- ✅ Individual wallet created automatically
- ✅ Unique transaction IDs for all transactions
- ✅ Proper balance tracking
- ✅ Transaction history access

## API Endpoints Fixed

### Primary Endpoints
- `GET /api/user/wallet` - Get wallet details
- `GET /api/user/wallet/transactions` - Get transaction history
- `GET /api/user/wallet/balance` - Get wallet balance
- `GET /api/user/wallet/statistics` - Get wallet statistics

### New Endpoint
- `POST /api/user/wallet/fix-duplicate-keys` - Fix existing duplicate keys

## Database Changes

### Indexes Modified
```javascript
// REMOVED: Problematic index
// walletSchema.index({ 'transactions.transactionId': 1 }, { sparse: true });

// ADDED: Better compound index
walletSchema.index({ user: 1, 'transactions.createdAt': -1 });
```

### Transaction ID Format
```
OLD: WTX_1234567890_ABC123
NEW: WTX_1234567890_USER123_ABC123
```

## Error Handling Improvements

### Before
```javascript
// Simple error - caused 500 errors
const wallet = await Wallet.findOrCreateWallet(userId);
```

### After
```javascript
// Robust error handling
try {
  wallet = await Wallet.findOrCreateWallet(userId);
} catch (createError) {
  if (createError.message.includes('E11000')) {
    // Handle duplicate key gracefully
    wallet = await Wallet.findOne({ user: userId });
  }
}
```

## Files Modified

1. **`souq-backend/db/models/walletModel.js`**
   - Removed problematic index
   - Enhanced transaction ID generation
   - Improved addTransaction method

2. **`souq-backend/app/user/wallet/controllers/walletController.js`**
   - Robust wallet creation
   - Improved error handling
   - Added cleanup function

3. **`souq-backend/app/user/wallet/routes/walletRoutes.js`**
   - Added cleanup route

## Prevention Measures

1. **Unique Transaction IDs** - Always generated with user context
2. **Graceful Error Handling** - Handles duplicate key errors
3. **Automatic Wallet Creation** - Creates wallets for new users
4. **Database Cleanup** - Function to fix existing issues
5. **Better Indexing** - Removed problematic sparse index

## Next Steps

1. **Run the cleanup function** to fix existing wallets
2. **Test with multiple users** to verify the fix
3. **Monitor logs** for any remaining issues
4. **Consider adding** wallet creation middleware for new users

The wallet functionality now supports unlimited users with proper isolation and error handling.
