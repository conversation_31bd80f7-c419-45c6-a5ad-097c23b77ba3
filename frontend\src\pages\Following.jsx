// pages/FollowersPage.jsx
import React, { useEffect, useState } from "react";
import { Fa<PERSON>lock, FaMapMarkerAlt, FaStar } from "react-icons/fa";
import { useNavigate, useParams } from "react-router-dom";
import { getProfileById } from "../api/AuthService";
import { getFollowing } from "../api/ProductService";
import FollowingCard from "../components/followers/FollowingCard";
import LoadingSpinner from "../components/common/LoadingSpinner";
import { formatDistanceToNowStrict } from "date-fns";
import { useTranslation } from "react-i18next";
import Pagination from '@mui/material/Pagination';
import Stack from '@mui/material/Stack';
import ConnectionCardSkeleton from "../components/Skeleton/ConnectionCardSkeleton";

const FollowingPage = () => {
    const { id } = useParams();
    const navigate = useNavigate()
    const [profile, setProfile] = useState(null);
    const [followersData, setFollowersData] = useState([]);
    const baseURL = import.meta.env.VITE_API_BASE_URL;
    const normalizedURL = baseURL.endsWith('/') ? baseURL.slice(0, -1) : baseURL;
    const authUser = JSON.parse(localStorage.getItem("user"));
    const { t } = useTranslation();
    const [page, setPage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);
    const [loading, setLoading] = useState(true);
    const limit = 20;

    useEffect(() => {
        getProfileById(id)
            .then((res) => {
                setProfile(res?.data?.data);
            })
            .catch((err) => {
                console.error("Failed to fetch profile", err);
            })
            .finally(() => {

            });
    }, [id]);

    useEffect(() => {
        setLoading(true);
        getFollowing(id, { page, limit })
            .then((res) => {
                const { following, totalPages } = res?.data?.data || {};
                setFollowersData(following || []);
                setTotalPages(totalPages || 0);
            })
            .catch((err) => {
                console.error("Failed to fetch following", err);
            })
            .finally(() => {
                setLoading(false);
            });
    }, [id, page]);

    if (!profile) {
        return <ConnectionCardSkeleton />;
    }

    const userNavigate = () => {
        if (authUser?.id === profile?.id) {
            navigate("/member-profile")
        } else {
            navigate(`/profile/${profile?.id}`)
        }
    }

    return (
        <div className="bg-white min-h-[600px] p-6 flex flex-col">
            <div className="container mx-auto flex flex-col flex-grow">
                {/* Header */}
                <div className="flex items-center gap-4 border-b pb-4 mb-6">
                    <img
                        src={profile?.profile ? `${normalizedURL}${profile?.profile}` : "https://cdn-icons-png.flaticon.com/512/149/149071.png"}
                        alt="profile"
                        className="w-16 h-16 rounded-full object-cover border border-gray-100 cursor-pointer"
                        onClick={userNavigate}
                    />
                    <div className="flex flex-col">
                        <span className="font-semibold text-gray-900">
                            {profile?.firstName} {profile?.lastName}
                        </span>
                        <div className="flex items-center text-yellow-500 text-sm mb-1">
                            {[...Array(5)].map((_, i) => (
                                <FaStar key={i} className="text-lg text-yellow-500" />
                            ))}
                            <span className="text-gray-600 text-xs ml-1">115</span>
                        </div>
                        <div className="text-sm text-gray-500 mt-1 space-y-1">
                            {profile?.country &&
                                <div className="flex items-center gap-2">
                                    <FaMapMarkerAlt className="text-sm" /> {profile?.city}, {profile?.country}
                                </div>}
                            <div className="flex items-center gap-2">
                                <FaClock className="text-sm" />
                                {profile?.lastLoginAt
                                    ? formatDistanceToNowStrict(new Date(profile?.lastLoginAt), { addSuffix: true })
                                    : "1 hour ago"}
                            </div>
                        </div>
                    </div>
                </div>

                <h2 className="text-lg font-semibold text-gray-900 mb-4">
                    {profile?.firstName} {profile?.lastName} {t("following")}
                </h2>

                {/* Content */}
                {loading ? (
                    <LoadingSpinner fullScreen={false} />
                ) : followersData.length === 0 ? (
                    <div className="flex justify-center items-center h-60 w-full">
                        <p className="text-center text-gray-500">
                            {profile?.firstName} {profile?.lastName} doesn’t follow anyone yet
                        </p>
                    </div>
                ) : (
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-4">
                        {followersData.map((person, index) => (
                            <FollowingCard key={index} person={person} />
                        ))}
                    </div>
                )}
            </div>

            {/* Pagination at the very bottom of the page */}
            {totalPages > 1 && (
                <div className="container mx-auto mt-6">
                    <div className="flex justify-end">
                        <Stack spacing={2}>
                            <Pagination
                                count={totalPages}
                                page={page}
                                onChange={(e, value) => setPage(value)}
                                color="gray"
                                variant="outlined"
                                shape="rounded"
                                size="medium"
                            />
                        </Stack>
                    </div>
                </div>
            )}
        </div>

    )
}

export default FollowingPage;