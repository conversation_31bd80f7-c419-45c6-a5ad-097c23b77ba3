# SOUQ Escrow System API Documentation

## 🔗 Base URL
```
Production: https://your-domain.com/api
Development: http://localhost:5000/api
```

## 🔐 Authentication
All protected endpoints require a Bearer token in the Authorization header:
```
Authorization: Bearer YOUR_JWT_TOKEN
```

## 📋 API Endpoints

### 🏦 Escrow Transactions

#### Create Escrow Transaction
```http
POST /user/escrow/create
```

**Request Body:**
```json
{
  "productId": "string",
  "offerId": "string (optional)",
  "paymentGateway": "paytabs|stripe|paypal",
  "shippingAddress": {
    "fullName": "string",
    "street1": "string",
    "street2": "string (optional)",
    "city": "string",
    "state": "string",
    "zip": "string",
    "country": "string"
  },
  "gatewayFeePaidBy": "buyer|seller",
  "currency": "AED|USD|EUR|GBP|SAR"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Escrow transaction created successfully",
  "data": {
    "escrowTransaction": {
      "_id": "string",
      "transactionId": "ESC-1703123456-ABC123",
      "status": "pending_payment",
      "productPrice": 299.99,
      "totalAmount": 329.99,
      "platformFeeAmount": 29.99,
      "currency": "AED"
    },
    "nextStep": "initialize_payment"
  }
}
```

#### Initialize Payment
```http
POST /user/escrow/{escrowTransactionId}/initialize-payment
```

**Request Body:**
```json
{
  "returnUrl": "https://your-domain.com/payment-success",
  "cancelUrl": "https://your-domain.com/payment-cancelled"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Payment initialized successfully",
  "data": {
    "paymentUrl": "https://secure.paytabs.com/payment/page/...",
    "transactionId": "TST2024001",
    "clientSecret": "pi_xxx_secret_xxx (for Stripe)",
    "publishableKey": "pk_test_xxx (for Stripe)"
  }
}
```

#### Get Escrow Transaction
```http
GET /user/escrow/{transactionId}
```

**Response:**
```json
{
  "success": true,
  "message": "Escrow transaction retrieved successfully",
  "data": {
    "escrowTransaction": {
      "_id": "string",
      "transactionId": "ESC-1703123456-ABC123",
      "status": "funds_held",
      "buyer": {
        "firstName": "Ahmed",
        "lastName": "Ali",
        "email": "<EMAIL>"
      },
      "seller": {
        "firstName": "Sara",
        "lastName": "Hassan",
        "email": "<EMAIL>"
      },
      "product": {
        "title": "Designer Handbag",
        "price": 299.99,
        "product_photos": ["photo1.jpg"]
      },
      "productPrice": 299.99,
      "totalAmount": 329.99,
      "platformFeeAmount": 29.99,
      "currency": "AED",
      "deliveryDetails": {
        "trackingNumber": "TRK123456789",
        "carrier": "Aramex",
        "shippedAt": "2024-01-15T10:30:00Z"
      }
    },
    "userRole": "buyer"
  }
}
```

#### Get User Escrow Transactions
```http
GET /user/escrow?role=buyer&status=completed&page=1&limit=10
```

**Query Parameters:**
- `role`: `buyer|seller|all` (default: all)
- `status`: Transaction status filter
- `page`: Page number (default: 1)
- `limit`: Items per page (default: 10)

#### Mark as Shipped (Seller Only)
```http
PATCH /user/escrow/{transactionId}/ship
```

**Request Body:**
```json
{
  "trackingNumber": "TRK123456789",
  "carrier": "Aramex",
  "estimatedDelivery": "2024-01-20T00:00:00Z"
}
```

#### Confirm Delivery (Buyer Only)
```http
PATCH /user/escrow/{transactionId}/confirm-delivery
```

### 💱 Currency Management

#### Get Supported Currencies
```http
GET /user/currency/supported
```

**Response:**
```json
{
  "success": true,
  "message": "Supported currencies retrieved successfully",
  "data": {
    "baseCurrency": "AED",
    "currencies": {
      "AED": {
        "code": "AED",
        "name": "UAE Dirham",
        "symbol": "د.إ",
        "rate": 1.0
      },
      "USD": {
        "code": "USD",
        "name": "US Dollar",
        "symbol": "$",
        "rate": 0.27
      }
    },
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

#### Convert Currency
```http
POST /user/currency/convert
```

**Request Body:**
```json
{
  "amount": 100,
  "fromCurrency": "AED",
  "toCurrency": "USD"
}
```

**Response:**
```json
{
  "success": true,
  "message": "Currency converted successfully",
  "data": {
    "originalAmount": 100,
    "originalCurrency": "AED",
    "convertedAmount": 27.23,
    "targetCurrency": "USD",
    "exchangeRate": 0.2723,
    "lastUpdated": "2024-01-15T10:30:00Z"
  }
}
```

#### Get Exchange Rates
```http
GET /user/currency/rates?baseCurrency=AED&targetCurrencies=USD,EUR,GBP
```

### 👨‍💼 Admin Endpoints

#### Get Dashboard Statistics
```http
GET /admin/escrow/dashboard/stats?period=30d
```

**Response:**
```json
{
  "success": true,
  "message": "Dashboard statistics retrieved successfully",
  "data": {
    "period": "30d",
    "overview": {
      "totalTransactions": 1247,
      "totalVolume": 456789.50,
      "totalPlatformFees": 45678.95,
      "completedTransactions": 1156,
      "pendingTransactions": 67,
      "successRate": "92.7",
      "averageTransactionValue": "366.45"
    },
    "breakdowns": {
      "status": [
        {
          "_id": "completed",
          "count": 1156,
          "totalAmount": 423456.78
        }
      ],
      "gateway": [
        {
          "_id": "paytabs",
          "count": 567,
          "totalAmount": 208934.56,
          "successRate": 0.94
        }
      ]
    }
  }
}
```

#### Get All Transactions (Admin)
```http
GET /admin/escrow/transactions?page=1&limit=20&status=completed&gateway=paytabs
```

#### Update Transaction Status (Admin)
```http
PATCH /admin/escrow/transactions/{transactionId}/status
```

**Request Body:**
```json
{
  "status": "completed",
  "note": "Manual completion by admin"
}
```

#### Process Manual Payout (Admin)
```http
POST /admin/escrow/transactions/{transactionId}/payout
```

### 🔗 Webhook Endpoints

#### Payment Gateway Webhooks
```http
POST /user/escrow/webhook/{gateway}
```

**Supported Gateways:**
- `paytabs`
- `stripe`
- `paypal`

**PayTabs Webhook Example:**
```json
{
  "tran_ref": "TST2024001",
  "payment_result": {
    "response_status": "A",
    "response_code": "100",
    "response_message": "Approved"
  }
}
```

**Stripe Webhook Example:**
```json
{
  "id": "evt_xxx",
  "type": "payment_intent.succeeded",
  "data": {
    "object": {
      "id": "pi_xxx",
      "status": "succeeded",
      "amount": 32999,
      "currency": "aed"
    }
  }
}
```

## 📊 Response Codes

| Code | Description |
|------|-------------|
| 200  | Success |
| 201  | Created |
| 400  | Bad Request |
| 401  | Unauthorized |
| 403  | Forbidden |
| 404  | Not Found |
| 500  | Internal Server Error |

## 🔄 Transaction Status Flow

```
pending_payment → payment_processing → funds_held → shipped → delivered → completed
                                    ↓
                                 payment_failed
                                    ↓
                                 cancelled
```

**Status Descriptions:**
- `pending_payment`: Waiting for buyer payment
- `payment_processing`: Payment being processed by gateway
- `payment_failed`: Payment failed or declined
- `funds_held`: Payment successful, funds held in escrow
- `shipped`: Seller marked item as shipped
- `delivered`: Buyer confirmed delivery
- `completed`: Transaction completed, payout processed
- `disputed`: Transaction under dispute
- `cancelled`: Transaction cancelled
- `refunded`: Payment refunded to buyer

## 🚨 Error Handling

**Standard Error Response:**
```json
{
  "success": false,
  "error": "Error message",
  "code": "ERROR_CODE",
  "details": {
    "field": "validation error details"
  }
}
```

**Common Error Codes:**
- `VALIDATION_ERROR`: Input validation failed
- `UNAUTHORIZED`: Authentication required
- `FORBIDDEN`: Insufficient permissions
- `NOT_FOUND`: Resource not found
- `PAYMENT_FAILED`: Payment processing failed
- `GATEWAY_ERROR`: Payment gateway error
- `CURRENCY_ERROR`: Currency conversion failed

## 🔧 Rate Limiting

- **General API**: 100 requests per 15 minutes per IP
- **Payment APIs**: 10 requests per minute per user
- **Webhook APIs**: 1000 requests per minute per gateway

## 📝 Notes

1. All monetary amounts are in the smallest currency unit (e.g., fils for AED)
2. Timestamps are in ISO 8601 format (UTC)
3. File uploads have a maximum size of 10MB
4. Webhook endpoints expect raw JSON body
5. Currency conversion rates update every hour
6. Auto-release occurs 7 days after shipping confirmation

## 🔗 SDKs and Libraries

### JavaScript/Node.js
```javascript
const SouqEscrow = require('@souq/escrow-sdk');

const escrow = new SouqEscrow({
  apiKey: 'your-api-key',
  baseUrl: 'https://your-domain.com/api'
});

// Create escrow transaction
const transaction = await escrow.createTransaction({
  productId: 'prod_123',
  paymentGateway: 'paytabs',
  currency: 'AED'
});
```

### React Hooks
```javascript
import { useEscrow } from '@souq/react-escrow';

function CheckoutPage() {
  const { createTransaction, loading, error } = useEscrow();
  
  const handleCheckout = async () => {
    const result = await createTransaction({
      productId: 'prod_123',
      paymentGateway: 'stripe'
    });
  };
}
```

**The SOUQ Escrow API is comprehensive and production-ready! 🚀**
