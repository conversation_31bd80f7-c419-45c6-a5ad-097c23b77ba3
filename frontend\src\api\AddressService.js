import axios from 'axios';

const API_BASE_URL = import.meta.env.VITE_API_BASE_URL;
const getToken = () => {
  return localStorage.getItem('accessToken');
};

// Create axios instance with default config
const addressAPI = axios.create({
  baseURL: `${API_BASE_URL}/api/user/addresses`,
  headers: {
    'Content-Type': 'application/json',
  },
});

// Add auth token to requests
addressAPI.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('accessToken');
    console.log('🔑 Address API - Token check:', token ? 'Token found' : 'No token found');
    console.log('🔗 Address API - Request URL:', config.url);

    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
      console.log('✅ Address API - Authorization header added');
    } else {
      console.warn('⚠️ Address API - No token found in localStorage');
    }
    return config;
  },
  (error) => {
    console.error('❌ Address API - Request interceptor error:', error);
    return Promise.reject(error);
  }
);

// Response interceptor for error handling
addressAPI.interceptors.response.use(
  (response) => {
    console.log('✅ Address API - Response received:', response.status);
    return response;
  },
  (error) => {
    console.error('❌ Address API Error:', {
      status: error.response?.status,
      message: error.response?.data?.message || error.message,
      url: error.config?.url
    });

    // Handle specific error cases
    if (error.response?.status === 401) {
      console.warn('🔒 Address API - Authentication required. User might need to log in.');
    }

    return Promise.reject(error);
  }
);

/**
 * Check if token is expired
 * @param {string} token - JWT token
 * @returns {boolean} Is expired
 */
const isTokenExpired = (token) => {
  try {
    const payload = JSON.parse(atob(token.split('.')[1]));
    const currentTime = Date.now() / 1000;
    return payload.exp < currentTime;
  } catch (error) {
    console.error('Error parsing token:', error);
    return true;
  }
};

/**
 * Check if user is authenticated
 * @returns {boolean} Is authenticated
 */
export const isAuthenticated = () => {
  const token = localStorage.getItem('accessToken');

  if (!token) {
    console.log('🔍 Authentication check: No token found');
    return false;
  }

  if (isTokenExpired(token)) {
    console.log('🔍 Authentication check: Token is expired');
    localStorage.removeItem('accessToken'); // Clean up expired token
    return false;
  }

  console.log('🔍 Authentication check: User is logged in with valid token');
  return true;
};

/**
 * Add a new address
 * @param {Object} addressData - Address details to add
 * @param {boolean} setAsDefault - Whether to set as default address
 * @returns {Promise<Object>} Add result
 */
export const addAddress = async (addressData, setAsDefault = false) => {
  try {
    // Check authentication before making request
    if (!isAuthenticated()) {
      throw { success: false, error: 'User not authenticated. Please log in.' };
    }

    console.log('🚀 Address API - Sending add address request...');
    const response = await addressAPI.post('/add', {
      ...addressData,
      setAsDefault
    });
    console.log('✅ Address API - Add address successful');
    return response.data;
  } catch (error) {
    console.error('❌ Address API - Add address failed:', error);
    throw error.response?.data || { success: false, error: 'Address add failed' };
  }
};

/**
 * Get user's addresses
 * @returns {Promise<Object>} User's addresses
 */
export const getUserAddresses = async () => {
  try {
    console.log('🚀 Address API - Fetching user addresses...');
    const response = await addressAPI.get('/');
    console.log('✅ Address API - Get addresses successful');
    return response.data;
  } catch (error) {
    console.error('❌ Address API - Get addresses failed:', error);
    throw error.response?.data || { success: false, error: 'Failed to fetch addresses' };
  }
};

/**
 * Get user's default address
 * @returns {Promise<Object>} Default address
 */
export const getDefaultAddress = async () => {
  try {
    console.log('🚀 Address API - Fetching default address...');
    const response = await addressAPI.get('/default');
    console.log('✅ Address API - Get default address successful');
    return response.data;
  } catch (error) {
    console.error('❌ Address API - Get default address failed:', error);
    throw error.response?.data || { success: false, error: 'Failed to fetch default address' };
  }
};

/**
 * Set an address as default
 * @param {string} addressId - Address ID to set as default
 * @returns {Promise<Object>} Update result
 */
export const setDefaultAddress = async (addressId) => {
  try {
    console.log('🚀 Address API - Setting default address...');
    const response = await addressAPI.put(`/${addressId}/set-default`);
    console.log('✅ Address API - Set default address successful');
    return response.data;
  } catch (error) {
    console.error('❌ Address API - Set default address failed:', error);
    throw error.response?.data || { success: false, error: 'Failed to set default address' };
  }
};

/**
 * Update an address
 * @param {string} addressId - Address ID to update
 * @param {Object} addressData - Updated address data
 * @returns {Promise<Object>} Update result
 */
export const updateAddress = async (addressId, addressData) => {
  try {
    console.log('🚀 Address API - Updating address...');
    const response = await addressAPI.put(`/${addressId}`, addressData);
    console.log('✅ Address API - Update address successful');
    return response.data;
  } catch (error) {
    console.error('❌ Address API - Update address failed:', error);
    throw error.response?.data || { success: false, error: 'Failed to update address' };
  }
};

/**
 * Delete an address
 * @param {string} addressId - Address ID to delete
 * @returns {Promise<Object>} Delete result
 */
export const deleteAddress = async (addressId) => {
  try {
    console.log('🚀 Address API - Deleting address...');
    const response = await addressAPI.delete(`/${addressId}`);
    console.log('✅ Address API - Delete address successful');
    return response.data;
  } catch (error) {
    console.error('❌ Address API - Delete address failed:', error);
    throw error.response?.data || { success: false, error: 'Failed to delete address' };
  }
};

/**
 * Validate address data (client-side validation)
 * @param {Object} addressData - Address data to validate
 * @returns {Object} Validation result
 */
export const validateAddress = (addressData) => {
  const errors = {};
  
  if (!addressData.fullName?.trim()) {
    errors.fullName = 'Full name is required';
  }
  
  if (!addressData.street1?.trim()) {
    errors.street1 = 'Street address is required';
  }
  
  if (!addressData.city?.trim()) {
    errors.city = 'City is required';
  }
  
  if (!addressData.zipCode?.trim()) {
    errors.zipCode = 'ZIP code is required';
  }
  
  if (!addressData.country?.trim()) {
    errors.country = 'Country is required';
  }
  
  // Validate ZIP code format (basic validation)
  // if (addressData.zipCode && !/^\d{5}(-\d{4})?$/.test(addressData.zipCode.trim())) {
  //   errors.zipCode = 'Invalid ZIP code format';
  // }
  
  return {
    isValid: Object.keys(errors).length === 0,
    errors
  };
};

/**
 * Format address for display
 * @param {Object} address - Address object
 * @returns {string} Formatted address string
 */
export const formatAddressDisplay = (address) => {
  if (!address) return '';
  
  const parts = [
    address.street1,
    address.street2,
    address.city,
    address.state,
    address.zipCode,
    address.country
  ].filter(part => part && part.trim());
  
  return parts.join(', ');
};

/**
 * Get address type display name
 * @param {string} type - Address type code
 * @returns {string} Display name
 */
export const getAddressTypeDisplayName = (type) => {
  const typeNames = {
    home: 'Home',
    work: 'Work',
    other: 'Other'
  };
  
  return typeNames[type] || 'Other';
};

export default {
  addAddress,
  getUserAddresses,
  getDefaultAddress,
  setDefaultAddress,
  updateAddress,
  deleteAddress,
  validateAddress,
  formatAddressDisplay,
  getAddressTypeDisplayName,
  isAuthenticated
};
