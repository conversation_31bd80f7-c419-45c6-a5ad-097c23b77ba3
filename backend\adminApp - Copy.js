require('dotenv').config();
const express = require('express');
const cors = require('cors');
const path = require('path');
const passport = require('passport');
require('./utils/passport');
const connectDB = require('./db');

const app = express();
const PORT = process.env.ADMIN_PORT || 5001;

connectDB();

app.use(cors({ origin: '*', credentials: true }));
app.use(express.json());
app.use(passport.initialize());

// Serve static files (images, uploads)
app.use('/uploads', express.static(path.join(__dirname, 'uploads')));

const adminRoutes = require('./app/admin/');
app.use('/api/admin', adminRoutes);

app.listen(PORT, () => {
  console.log(`🚀 Admin API running on http://localhost:${PORT}`);
});


