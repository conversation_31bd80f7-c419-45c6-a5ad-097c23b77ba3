export const mockNotifications = [
  {
    id: '1',
    title: 'Item sold!',
    message: 'Your Nike Air Max has been sold to <PERSON>',
    timestamp: '2 minutes ago',
    isRead: false,
    type: 'sale',
    avatar: 'https://images.pexels.com/photos/774909/pexels-photo-774909.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop'
  },
  {
    id: '2',
    title: 'New message',
    message: '<PERSON> sent you a message about "Vintage Leather Jacket"',
    timestamp: '15 minutes ago',
    isRead: false,
    type: 'message',
    avatar: 'https://images.pexels.com/photos/1222271/pexels-photo-1222271.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop'
  },
  {
    id: '3',
    title: 'Price drop alert',
    message: 'The item you favorited is now 20% off!',
    timestamp: '1 hour ago',
    isRead: false,
    type: 'promotion'
  },
  {
    id: '4',
    title: 'Item shipped',
    message: 'Your order #VT-12345 has been shipped',
    timestamp: '3 hours ago',
    isRead: true,
    type: 'system'
  },
  {
    id: '5',
    title: 'New follower',
    message: '<PERSON> started following you',
    timestamp: '5 hours ago',
    isRead: true,
    type: 'system',
    avatar: 'https://images.pexels.com/photos/1130626/pexels-photo-1130626.jpeg?auto=compress&cs=tinysrgb&w=50&h=50&fit=crop'
  },
  {
    id: '6',
    title: 'Bundle offer',
    message: 'Someone made a bundle offer on 3 of your items',
    timestamp: '6 hours ago',
    isRead: true,
    type: 'sale'
  },
  {
    id: '7',
    title: 'Account security',
    message: 'Your password was successfully changed',
    timestamp: '1 day ago',
    isRead: true,
    type: 'system'
  },
  {
    id: '8',
    title: 'Weekend sale',
    message: 'Get 30% off on all electronics this weekend!',
    timestamp: '2 days ago',
    isRead: true,
    type: 'promotion'
  },
  {
    id: '9',
    title: 'Item liked',
    message: 'Your Adidas Sneakers received 5 new likes',
    timestamp: '2 days ago',
    isRead: true,
    type: 'system'
  },
  {
    id: '10',
    title: 'Payment received',
    message: 'You received €45.00 from your recent sale',
    timestamp: '3 days ago',
    isRead: true,
    type: 'sale'
  }
];
