{"welcome": "Welcome to our site", "language": "Language", "souq": "SOUQ", "catalog": "Catalog", "member": "Members", "help_center": "Help Center", "search_for_item": "Search for items", "search_for_member": "Search for members", "sign_up": "Sign up", "log_in": "Log in", "sell_now": "Sell now", "profile": "Profile", "settings": "Settings", "personalization": "Personalization", "wallet": "Wallet", "my_orders": "My Orders", "donation": "Donation", "invite_friends": "Invite Friends", "logout": "Logout", "ready_to_declutter": "Ready to declutter your closet?", "learn_how_it_works": "Learn how it works", "category": "Category", "brand": "Brand", "size": "Size", "condition": "Condition", "color": "Color", "material": "Material", "price": "Price", "sort_by": "Sort by", "newsfeed": "Newsfeed", "footer_description": "The go-to marketplace for buying and selling secondhand fashion.", "company": "Company", "about": "About", "careers": "Careers", "press": "Press", "sustainability": "Sustainability", "support": "Support", "safety_center": "Safety Center", "community_guidelines": "Community Guidelines", "contact_us": "Contact Us", "legal": "Legal", "privacy_policy": "Privacy Policy", "terms_of_service": "Terms of Service", "cookie_policy": "<PERSON><PERSON>", "accessibility": "Accessibility", "all_rights_reserved": "All rights reserved.", "join_and_sell": "Join and sell pre-loved clothes with no fees", "welcome_back": "Welcome back", "continue_with_google": "Continue with Google", "signup_with_google": "Sign up with Google", "continue_with_apple": "Continue with Apple", "signup_with_apple": "Sign up with Apple", "continue_with_facebook": "Continue with Facebook", "signup_with_facebook": "Sign up with Facebook", "login_with": "Log in with", "register_with": "Or register with", "email": "Email", "dont_have_account": "Don’t have an account yet?", "already_have_account": "Already have an account?", "login_with_email": "Log in with <PERSON><PERSON>", "close_login_modal": "Close login modal", "password": "Password", "email_required": "Email is required", "invalid_email": "Enter a valid email address", "password_required": "Password is required", "password_min_length": "Password must be at least 7 characters", "logging_in": "Logging in...", "forgot_password_your": "Forgot your password?", "forgot_password": "Forgot Password", "reset_email_sent": "If this email address is registered with <PERSON><PERSON><PERSON>, then we sent your password reset instructions there.", "enter_email_reset_link": "Enter your email and we'll send you a reset link.", "send_reset_link": "Send Reset Link", "sending": "Sending...", "close_forgot_password_modal": "Close forgot password modal", "signup_with_email": "Sign up with email", "full_name": "Full name", "full_name_required": "Full name is required", "enter_full_name": "Enter your first and last names", "full_name_note": "Your full name will not be publicly visible.", "username": "Username", "username_required": "Username is required", "username_note": "Use only letters and numbers. Usernames can’t be changed later.", "email_note": "Enter the email you want to use on <PERSON><PERSON>", "password_pattern": "Must contain at least 1 letter and 1 number", "password_note": "At least 7 characters, with at least 1 letter and 1 number.", "promo_opt_in": "I’d like to receive personalized offers and updates via email.", "agree_terms_prefix": "By clicking continue, I agree to", "terms_conditions": "Souq Terms & Conditions", "terms_required": "Please accept the terms and conditions.", "continue": "Continue", "close_signup_modal": "Close signup modal", "email_verification": "Email Verification", "enter_verification_code": "Enter the verification code that we sent to this email address:", "enter_code_placeholder": "Enter 6-digit code", "code_required": "Verification code is required", "code_6_digits": "Code must be 6 digits", "only_numbers": "Only numbers are allowed", "verify": "Verify", "verifying": "Verifying...", "didnt_receive_email": "Didn't receive our mail?", "enter_phone_number": "Enter Your Phone Number", "send_otp": "Send OTP", "favourited_item": "Favorited items", "no_favorite_items": "No favorite items found", "browse": "Browse", "price_breakdown": "Price breakdown", "item": "<PERSON><PERSON>", "buyer_protection_fee": "Buyer Protection fee", "shipping_note": "Shipping fees and sales tax are calculated at checkout", "buyer_protection_info": "Our Buyer Protection fee is mandatory when you purchase an item on Vinted. It is added to every purchase made with the “Buy Now” button. The item price is set by the seller and may be subject to negotiation.", "ok_close": "Ok, close", "categories": "Categories", "my_profile": "My Profile", "edit_profile": "Edit Profile", "last_seen": "Last seen", "followers": "Followers", "following": "Following", "verified_info": "Verified Info", "google": "Google", "facebook": "Facebook", "listings": "Listings", "reviews": "Reviews", "insights": "Insights", "follow": "Follow", "profile_details": "Profile details", "account_settings": "Account settings", "shipping": "Shipping", "payments": "Payments", "bundle_discounts": "Bundle discounts", "notifications": "Notifications", "privacy_settings": "Privacy settings", "security": "Security", "your_photo": "Your photo", "choose_photo": "Choose photo", "username_change_note": "If you change your username, you won’t be able to modify it again for 30 days.", "enter_username": "Enter username", "about_you": "About you", "about_you_placeholder": "Tell us more about yourself and your style", "country": "Country", "united_states": "United States", "united_kingdom": "United Kingdom", "india": "India", "canada": "Canada", "australia": "Australia", "town_city": "Town/City", "mumbai": "Mumbai", "delhi": "Delhi", "new_york": "New York", "london": "London", "show_city_in_profile": "Show city in profile", "english": "English", "hindi": "Hindi", "french": "French", "german": "German", "update_profile": "Update Profile", "about_placeholder": "Tell us more about yourself and your style", "city": "Town/City", "show_city": "Show city in profile", "change": "Change", "verified": "Verified ✓", "not_verified": "Not Verified", "phone_number": "Phone number", "not_added": "Not added", "verified_short": "Verified", "personal_info": "Personal Information", "gender": "Gender", "gender_required": "Gender is required", "select_gender": "Select gender", "male": "Male", "female": "Female", "other": "Other", "birthday": "Birthday", "vacation_mode": "Vacation mode", "linked": "Linked", "link": "Link", "social_note": "Link to your other accounts to become a trusted, verified member.", "change_password": "Change password", "delete_account": "Delete my account", "save": "Save", "delete_reason": "Help us improve", "delete_reason_placeholder": "Tell us why you’re closing your account", "confirm_delete_note": "I confirm that all my orders are completed", "delete_info": "If you delete your account, it will be deactivated immediately. Deactivated accounts are only visible to Team Vinted before they are permanently deleted. The deletion takes place within the time frames indicated in Vinted’s Privacy Policy.", "cancel": "Cancel", "delete_account_button": "Delete account", "phone_usage_note": "Your phone number will only be used to help you log in. It won’t be public or used for marketing.", "your_address": "Your address", "edit": "Edit", "address_line1": "Address Line 1", "address_line2": "Address Line 2 (optional)", "zip_code": "Zip Code", "save_address": "Save address", "add_address": "Add Address", "shipping_title": "Shipping as a seller", "shipping_info": "Choose which options you’d like to use for each shipping type.", "from_address": "From your address", "from_address_note": "A courier collects the order from you.", "no_options": "Currently, there are no options that ship from your address.", "dropoff": "From a drop-off point", "dropoff_note": "You take the order to a location like a locker or package shop.", "shipping_disabling": "Disabling shipping options may reduce sales. If a member can only buy from you with a disabled option, we may still offer it.", "learn_more": "Learn more about disabled options.", "note_bottom": "Some shipping options are enabled for all sellers on our platform and can’t be turned off.", "compensation_info": "See compensation information", "for_seller_using_integrate": " for sellers using integrated shipping", "paymentOptions": "Payment options", "addNewCard": "Add new card", "payoutOptions": "Payout options", "addBankAccount": "Add bank account", "cardDetails": "Card details", "cardInfoSecure": "Your card information is securely encrypted", "cardHolder": "Cardholder's name", "cardNumber": "Card number", "expiryDate": "Expiration date", "securityCode": "Security code", "useCard": "Use this card", "accountDetails": "Account details", "accountHolder": "Account holder's name", "accountNumber": "Account No.", "routingNumber": "Routing No.", "accountType": "Account type", "billingAddress": "Billing address", "checking": "Checking", "savings": "Savings", "enableBundleDiscounts": "Enable bundle discounts", "enable": "Enable", "bundleDiscountNote": "Encourage people to buy more items from you with bundle discounts. Set rates based on the number of items per order. Learn more at the", "helpCentre": "Help Centre", "itemCount": "{{count}} items", "selectDiscount": "--", "enableEmailNotifications": "Enable email notifications", "vintedUpdates": "Vinted Updates", "marketingCommunications": "Marketing communications", "newMessages": "New messages", "newFeedback": "New feedback", "discountedItems": "Discounted items", "favoritedItems": "Favorited Items", "newFollowers": "New followers", "newItems": "New items", "dailyLimit": "Set a daily limit for each notification type", "limit2": "Up to 2 notifications", "limit5": "Up to 5 notifications", "noLimit": "No limit", "news": "News", "highPriority": "High-priority notifications", "otherNotifications": "Other notifications", "privacySettings": "Privacy settings", "featureInMarketing": "Feature my items in marketing campaigns for a chance to sell faster", "featureInMarketingDesc": "This allows <PERSON><PERSON> to showcase my items on social media and other websites. The increased visibility could lead to quicker sales.", "notifyFavorites": "Notify owners when I favorite their items", "allowTracking": "Allow third-party tracking", "personalizedFeed": "Allow Vinted to personalize my feed and search results by evaluating my preferences, settings, previous purchases and usage of Vinted website and app", "showRecentlyViewed": "Allow Vinted to display my recently viewed items on my Homepage.", "showRecentlyViewedDesc": "If you turn this option off but allow personalized content, these items will still be used to personalize your Feed.", "downloadData": "Download account data", "downloadDataDesc": "Request a copy of your Vinted account data.", "toggle": "Toggle", "heading": "Keep your account secure", "subheading": "Review your info to help protect your account.", "emailTitle": "Email", "emailDesc": "Keep your email up to date.", "passwordTitle": "Password", "passwordDesc": "Protect your account with a stronger password.", "verificationTitle": "2-step verification", "verificationDesc": "Confirm new logins with a 4-digit code.", "loginTitle": "Login activity", "loginDesc": "Manage your logged-in devices.", "title_confirm": "Confirm change", "description_confirm": "You need to confirm", "sendBtn": "Send confirmation email", "noAccess": "I don't have access to this email", "is_your_email_is": "is your email address before you can update it", "change_password_title": "Change Password", "new_password_placeholder": "New Password", "confirm_password_placeholder": "Confirm Password", "new_password_required": "Password is required", "confirm_password_required": "Please confirm your password", "passwords_not_match": "Passwords do not match", "change_password_button": "Change Password", "updating_password": "Updating...", "login_activity": "Review login activity", "login_session": "Each session shows a device logged into your account. If you notice any unusual activity, log out of that session.", "currentDevice": "- Current <PERSON><PERSON>", "phoneVerifyTitle": "Phone Verification", "phoneVerifyInstruction": "Enter the 6-digit code sent to your phone number:", "codePlaceholder": "Enter verification code", "resend": "Didn’t receive the code? Resend", "sellAnItem": "Sell an item", "editItem": "Edit item", "photos": "Photos", "addPhotosHint": "Add up to 20 photos. The first image will be your cover photo.", "dragPhotosHere": "Drag photos here", "clickToBrowse": "or click to browse from your device", "uploadPhotos": "Upload photos", "itemDetails": "Item details", "title": "Title", "description": "Description", "itemAttributes": "Item attributes", "packageSize": "Package size", "packageSizeHint": "Select the appropriate package size for your item. The shipping rate will depend on the package size", "small": "Small", "medium": "Medium", "large": "Large", "custom": "Custom", "enterCustomShipping": "Enter custom shipping cost", "saveAsDraft": "Save as draft", "upload": "Upload", "coverPhoto": "Cover photo", "addMore": "Add more", "uploading": "Uploading..", "selectCategory": "Select category", "selectBrand": "Select brand", "selectSize": "Select size", "selectCondition": "Select condition", "selectColor": "Select color", "selectMaterial": "Select material", "exampleItem": "e.g. <PERSON> Levi's <PERSON>s", "describeItem": "Describe your item: include information on fit, color, size, material, and condition.", "includesBuyerProtection": "Includes Buyer Protection", "more": "More", "showLess": "Show Less", "buyNow": "Buy now", "makeAnOffer": "Make an offer", "messageSeller": "Message seller", "buyerProtectionFee": "Buyer Protection fee", "buyerProtectionInfo": "Our Buyer Protection is added for a fee to every purchase made with the \"Buy now\" button. Buyer Protection includes our refund policy.", "bump": "Bump", "markAsSold": "Mark as sold", "markAsReserved": "Mark as reserved", "hide": "<PERSON>de", "editListing": "Edit listing", "delete": "Delete", "hidden": "Hidden", "unhide": "Unhide", "membersItems": "Member's items", "uploaded": "Uploaded", "views": "Views", "favorites": "Favorites", "areYouSure": "Are you sure?", "deleteConfirmation": "Do you really want to delete this product? This action cannot be undone.", "deleting": "Deleting...", "delivery": "Delivery", "change_email": "Change Email Address", "current_email": "Current Email", "enter_new_email": "Enter new email address", "send_verification_code": "Send Verification Code", "verification_code_sent": "Verification code sent to your updated email", "email_change_failed": "Failed to change email", "something_went_wrong": "Something went wrong. Please try again.", "edit_address": "Edit Address", "state_province": "State/Province", "address_type": "Address Type", "saving": "Saving...", "delete_confirmation": "Are you sure you want to delete this {{itemType}}? This action cannot be undone.", "address": "Address", "delivery_settings": "Delivery Settings", "manage_delivery": "Manage your delivery options, providers, and local pickup/drop-off locations", "shipping_providers": "Shipping Providers", "local_delivery": "Local Delivery", "add_delivery_option": "Add Delivery Option", "no_delivery_options": "No delivery options configured", "add_first_delivery_option": "Add Your First Delivery Option", "default": "<PERSON><PERSON><PERSON>", "insurance": "Insurance", "signature_required": "Signature Required", "cash_on_delivery": "Cash on Delivery", "available_shipping_providers": "Available Shipping Providers", "services_available": "Services available", "base_fee": "Base fee", "edit_delivery_option": "Edit Delivery Option", "shipping_provider": "Shipping Provider", "select_provider": "Select a provider", "service": "Service", "select_service": "Select a service", "preferences": "Preferences", "include_insurance": "Include insurance", "require_signature": "Require signature", "allow_cod": "Allow cash on delivery", "set_default": "Set as default delivery option", "save_option": "Save Option", "shipping_address": "Shipping address", "pickup_locations": "Pickup Locations", "pickup_description": "Locations where buyers can pick up their orders", "add_pickup_location": "Add Pickup Location", "no_pickup_locations": "No pickup locations configured", "active_time_slots": "Active time slots", "dropoff_locations": "Drop-off Locations", "dropoff_description": "Locations where you can drop off orders for delivery", "add_dropoff_location": "Add Drop-off Location", "no_dropoff_locations": "No drop-off locations configured", "operating_hours_configured": "Operating hours configured", "location_name": "Location Name", "operating_hours": "Operating Hours", "save_location": "Save Location", "local_delivery_address": "Local delivery address", "edit_pickup_location": "Edit Pickup Location", "edit_dropoff_location": "Edit Drop-off Location", "new": "New", "mark_all_read": "<PERSON> all read", "no_notifications": "No notifications yet", "notify_when_happens": "We'll notify you when something happens", "view_all_notifications": "View all notifications", "unread_notifications": "Unread Notifications", "all": "All", "unread": "Unread", "read": "Read"}