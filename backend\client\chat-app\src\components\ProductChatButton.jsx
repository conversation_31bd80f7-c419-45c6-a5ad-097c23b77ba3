import React, { useState } from 'react';
import ChatRoom from './ChatRoom';

export default function ProductChatButton({ 
  productId, 
  currentUserId, 
  buttonText = "Message seller",
  buttonStyle = {},
  disabled = false 
}) {
  const [showChat, setShowChat] = useState(false);

  const handleChatClick = () => {
    if (!disabled && productId && currentUserId) {
      setShowChat(true);
    }
  };

  const handleCloseChat = () => {
    setShowChat(false);
  };

  const defaultButtonStyle = {
    width: '100%',
    padding: '12px 24px',
    backgroundColor: '#28a745',
    color: 'white',
    border: 'none',
    borderRadius: '6px',
    fontSize: '16px',
    fontWeight: 'bold',
    cursor: disabled ? 'not-allowed' : 'pointer',
    opacity: disabled ? 0.6 : 1,
    transition: 'background-color 0.2s',
    ...buttonStyle
  };

  return (
    <>
      <button
        onClick={handleChatClick}
        disabled={disabled}
        style={defaultButtonStyle}
        onMouseEnter={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = '#218838';
          }
        }}
        onMouseLeave={(e) => {
          if (!disabled) {
            e.target.style.backgroundColor = buttonStyle.backgroundColor || '#28a745';
          }
        }}
      >
        {buttonText}
      </button>

      {/* Chat Modal/Overlay */}
      {showChat && (
        <div style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          backgroundColor: 'rgba(0, 0, 0, 0.5)',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          zIndex: 1000,
          padding: '20px'
        }}>
          <div style={{
            width: '100%',
            maxWidth: '600px',
            maxHeight: '90vh',
            backgroundColor: 'white',
            borderRadius: '8px',
            overflow: 'hidden',
            boxShadow: '0 4px 20px rgba(0, 0, 0, 0.15)'
          }}>
            <ChatRoom
              productId={productId}
              currentUserId={currentUserId}
              onClose={handleCloseChat}
            />
          </div>
        </div>
      )}
    </>
  );
}
