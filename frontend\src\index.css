@tailwind base;
@tailwind components;
@tailwind utilities;

/* Hide scrollbar for Chrome, Safari and Opera */
.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

/* Hide scrollbar for IE, Edge and Firefox */
.hide-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

@layer base {
  body {
    @apply bg-gray-50 text-gray-900 antialiased;
  }
}

/* .phone-input-border{
  border: 1px solid red !important;
} */

input[type="number"]::-webkit-inner-spin-button,
input[type="number"]::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

input[type="number"] {
  -moz-appearance: textfield;
  /* Firefox */
}

:dir(rtl) .react-tel-input .selected-flag .flag {
  left: 7px;
}

:dir(rtl) .react-tel-input .selected-flag .arrow {
  left: 11px;
}

.react-tel-input .form-control {
  height: 44px;
  font-size: 16px
}

.css-5qgam0-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer {
  padding: 12px !important;
}

.css-4k4mmf-MuiButtonBase-root-MuiPickersDay-root.Mui-selected {
  background-color: rgb(13 148 136) !important;
}

.css-1mvmid-MuiYearCalendar-button.Mui-selected {
  background-color: rgb(13 148 136) !important;
}

.css-1jyby0c-MuiMonthCalendar-button.Mui-selected {
  background-color: rgb(13 148 136) !important;
}

.css-gt5462-MuiPickersInputBase-root-MuiPickersInput-root {
  padding: 5 !important;
}

.css-4zowcb-MuiPickersSectionList-root-MuiPickersInputBase-sectionsContainer {
  padding: 12px !important;
}

.css-qct7wd-MuiButtonBase-root-MuiPickersDay-root.Mui-selected {
  background-color: rgb(13 148 136) !important;
}