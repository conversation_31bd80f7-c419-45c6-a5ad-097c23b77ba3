import React, { useEffect, useState, useRef } from 'react';
import io from 'socket.io-client';
import axios from 'axios';

// Get token from localStorage (you should implement proper token management)
const getToken = () => {
  return localStorage.getItem('authToken') || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.******************************************************************************************************************************************.9Z_OuDvWKivfnJQ6OM352Rdk5QkypiRoZdHkwY4xopc';
};

const API_BASE_URL = 'http://localhost:5000/api/user';

export default function ChatRoom({ productId, currentUserId, onClose }) {
  const [chat, setChat] = useState(null);
  const [messages, setMessages] = useState([]);
  const [text, setText] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);
  const [isTyping, setIsTyping] = useState(false);
  const [otherUserTyping, setOtherUserTyping] = useState(false);
  const [socket, setSocket] = useState(null);
  const [connected, setConnected] = useState(false);

  const messagesEndRef = useRef(null);
  const typingTimeoutRef = useRef(null);
  const token = getToken();

  // Scroll to bottom of messages
  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [messages]);

  // Initialize socket connection
  useEffect(() => {
    const newSocket = io('http://localhost:5000', {
      auth: { token }
    });

    newSocket.on('connect', () => {
      console.log('Connected to server');
      setConnected(true);
    });

    newSocket.on('disconnect', () => {
      console.log('Disconnected from server');
      setConnected(false);
    });

    newSocket.on('error', (error) => {
      console.error('Socket error:', error);
      setError(error.message);
    });

    setSocket(newSocket);

    return () => {
      newSocket.close();
    };
  }, [token]);

  // Initialize chat when productId changes
  useEffect(() => {
    if (!productId || !socket || !connected) return;

    initializeChat();
  }, [productId, socket, connected]);

  const initializeChat = async () => {
    try {
      setLoading(true);
      setError(null);

      // Create or get chat for this product
      const chatResponse = await axios.post(
        `${API_BASE_URL}/chat/product/${productId}`,
        {},
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      const chatData = chatResponse.data.data.chat;
      setChat(chatData);

      // Join the chat room
      socket.emit('join_chat', {
        chatId: chatData.id,
        roomId: chatData.roomId
      });

      // Fetch existing messages
      const messagesResponse = await axios.get(
        `${API_BASE_URL}/chat/${chatData.id}/messages`,
        {
          headers: {
            Authorization: `Bearer ${token}`
          }
        }
      );

      setMessages(messagesResponse.data.data.messages || []);
      setLoading(false);

    } catch (err) {
      console.error('Initialize chat error:', err);
      setError(err.response?.data?.message || 'Failed to initialize chat');
      setLoading(false);
    }
  };

  // Socket event listeners
  useEffect(() => {
    if (!socket) return;

    // Listen for new messages
    socket.on('new_message', (messageData) => {
      setMessages(prev => [...prev, messageData]);
    });

    // Listen for typing indicators
    socket.on('user_typing', (data) => {
      if (data.user.id !== currentUserId) {
        setOtherUserTyping(data.isTyping);
      }
    });

    // Listen for messages seen
    socket.on('messages_seen', (data) => {
      setMessages(prev => prev.map(msg => ({
        ...msg,
        seen: msg.sender.id === currentUserId ? true : msg.seen
      })));
    });

    // Listen for user joined/left
    socket.on('user_joined', (data) => {
      console.log(`${data.user.userName} joined the chat`);
    });

    socket.on('user_left', (data) => {
      console.log(`${data.user.userName} left the chat`);
    });

    return () => {
      socket.off('new_message');
      socket.off('user_typing');
      socket.off('messages_seen');
      socket.off('user_joined');
      socket.off('user_left');
    };
  }, [socket, currentUserId]);

  // Handle typing
  const handleTyping = (value) => {
    setText(value);

    if (!socket || !chat) return;

    // Start typing indicator
    if (!isTyping) {
      setIsTyping(true);
      socket.emit('typing_start', {
        roomId: chat.roomId,
        chatId: chat.id
      });
    }

    // Clear existing timeout
    if (typingTimeoutRef.current) {
      clearTimeout(typingTimeoutRef.current);
    }

    // Stop typing after 2 seconds of inactivity
    typingTimeoutRef.current = setTimeout(() => {
      setIsTyping(false);
      socket.emit('typing_stop', {
        roomId: chat.roomId,
        chatId: chat.id
      });
    }, 2000);
  };

  // Send message
  const sendMessage = () => {
    if (!text.trim() || !socket || !chat) return;

    socket.emit('send_message', {
      chatId: chat.id,
      roomId: chat.roomId,
      text: text.trim(),
      messageType: 'text'
    });

    setText('');

    // Stop typing indicator
    if (isTyping) {
      setIsTyping(false);
      socket.emit('typing_stop', {
        roomId: chat.roomId,
        chatId: chat.id
      });
    }
  };

  // Handle Enter key press
  const handleKeyPress = (e) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      sendMessage();
    }
  };

  // Mark messages as seen when component mounts or chat changes
  useEffect(() => {
    if (socket && chat) {
      socket.emit('mark_seen', {
        chatId: chat.id,
        roomId: chat.roomId
      });
    }
  }, [socket, chat]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (socket && chat) {
        socket.emit('leave_chat', {
          roomId: chat.roomId
        });
      }
      if (typingTimeoutRef.current) {
        clearTimeout(typingTimeoutRef.current);
      }
    };
  }, [socket, chat]);

  if (loading) {
    return (
      <div style={{ padding: 20, textAlign: 'center' }}>
        <div>Loading chat...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div style={{ padding: 20, textAlign: 'center', color: 'red' }}>
        <div>Error: {error}</div>
        <button onClick={initializeChat} style={{ marginTop: 10, padding: '8px 16px' }}>
          Retry
        </button>
      </div>
    );
  }

  if (!chat) {
    return (
      <div style={{ padding: 20, textAlign: 'center' }}>
        <div>No chat available</div>
      </div>
    );
  }

  const otherUser = chat.buyer.id === currentUserId ? chat.seller : chat.buyer;

  return (
    <div style={{
      display: 'flex',
      flexDirection: 'column',
      height: '600px',
      border: '1px solid #ddd',
      borderRadius: '8px',
      backgroundColor: '#fff'
    }}>
      {/* Chat Header */}
      <div style={{
        padding: '16px',
        borderBottom: '1px solid #eee',
        backgroundColor: '#f8f9fa',
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center'
      }}>
        <div>
          <h3 style={{ margin: 0, fontSize: '18px' }}>
            {otherUser.firstName} {otherUser.lastName} (@{otherUser.userName})
          </h3>
          <p style={{ margin: '4px 0 0 0', fontSize: '14px', color: '#666' }}>
            Product: {chat.product.title}
          </p>
          <div style={{ fontSize: '12px', color: connected ? 'green' : 'red' }}>
            {connected ? '● Online' : '● Offline'}
          </div>
        </div>
        {onClose && (
          <button
            onClick={onClose}
            style={{
              background: 'none',
              border: 'none',
              fontSize: '20px',
              cursor: 'pointer',
              padding: '4px 8px'
            }}
          >
            ×
          </button>
        )}
      </div>

      {/* Messages Area */}
      <div style={{
        flex: 1,
        overflowY: 'auto',
        padding: '16px',
        backgroundColor: '#f5f5f5'
      }}>
        {messages.length === 0 ? (
          <div style={{ textAlign: 'center', color: '#666', marginTop: '50px' }}>
            No messages yet. Start the conversation!
          </div>
        ) : (
          messages.map((msg, index) => {
            const isOwnMessage = msg.sender.id === currentUserId;
            const showAvatar = index === 0 || messages[index - 1].sender.id !== msg.sender.id;

            return (
              <div key={msg.id} style={{
                marginBottom: '12px',
                display: 'flex',
                justifyContent: isOwnMessage ? 'flex-end' : 'flex-start'
              }}>
                <div style={{
                  maxWidth: '70%',
                  display: 'flex',
                  flexDirection: isOwnMessage ? 'row-reverse' : 'row',
                  alignItems: 'flex-end'
                }}>
                  {showAvatar && (
                    <div style={{
                      width: '32px',
                      height: '32px',
                      borderRadius: '50%',
                      backgroundColor: isOwnMessage ? '#007bff' : '#6c757d',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                      color: 'white',
                      fontSize: '12px',
                      fontWeight: 'bold',
                      margin: isOwnMessage ? '0 0 0 8px' : '0 8px 0 0'
                    }}>
                      {(isOwnMessage ? 'You' : msg.sender.firstName?.charAt(0) || 'U')}
                    </div>
                  )}
                  <div style={{
                    backgroundColor: isOwnMessage ? '#007bff' : '#fff',
                    color: isOwnMessage ? 'white' : 'black',
                    padding: '8px 12px',
                    borderRadius: '18px',
                    border: isOwnMessage ? 'none' : '1px solid #ddd',
                    marginLeft: !showAvatar && !isOwnMessage ? '40px' : '0',
                    marginRight: !showAvatar && isOwnMessage ? '40px' : '0'
                  }}>
                    <div>{msg.text}</div>
                    <div style={{
                      fontSize: '11px',
                      opacity: 0.7,
                      marginTop: '4px',
                      display: 'flex',
                      justifyContent: 'space-between',
                      alignItems: 'center'
                    }}>
                      <span>{new Date(msg.createdAt).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}</span>
                      {isOwnMessage && (
                        <span style={{ marginLeft: '8px' }}>
                          {msg.seen ? '✓✓' : '✓'}
                        </span>
                      )}
                    </div>
                  </div>
                </div>
              </div>
            );
          })
        )}

        {/* Typing indicator */}
        {otherUserTyping && (
          <div style={{
            display: 'flex',
            alignItems: 'center',
            color: '#666',
            fontSize: '14px',
            marginTop: '8px'
          }}>
            <div style={{
              width: '32px',
              height: '32px',
              borderRadius: '50%',
              backgroundColor: '#6c757d',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center',
              color: 'white',
              fontSize: '12px',
              fontWeight: 'bold',
              marginRight: '8px'
            }}>
              {otherUser.firstName?.charAt(0) || 'U'}
            </div>
            <div style={{
              backgroundColor: '#fff',
              padding: '8px 12px',
              borderRadius: '18px',
              border: '1px solid #ddd'
            }}>
              <span>typing</span>
              <span className="typing-dots">...</span>
            </div>
          </div>
        )}

        <div ref={messagesEndRef} />
      </div>

      {/* Message Input */}
      <div style={{
        padding: '16px',
        borderTop: '1px solid #eee',
        backgroundColor: '#fff'
      }}>
        <div style={{ display: 'flex', gap: '8px' }}>
          <textarea
            value={text}
            onChange={(e) => handleTyping(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            style={{
              flex: 1,
              padding: '12px',
              border: '1px solid #ddd',
              borderRadius: '20px',
              resize: 'none',
              minHeight: '20px',
              maxHeight: '100px',
              fontFamily: 'inherit',
              fontSize: '14px'
            }}
            rows={1}
          />
          <button
            onClick={sendMessage}
            disabled={!text.trim() || !connected}
            style={{
              padding: '12px 20px',
              backgroundColor: text.trim() && connected ? '#007bff' : '#ccc',
              color: 'white',
              border: 'none',
              borderRadius: '20px',
              cursor: text.trim() && connected ? 'pointer' : 'not-allowed',
              fontWeight: 'bold'
            }}
          >
            Send
          </button>
        </div>
      </div>
    </div>
  );
}
