const express = require('express');
const router = express.Router();
const verifyToken = require('../../../../utils/verifyToken');
const orderController = require('../controllers/orderController');

// All order routes require authentication
router.use(verifyToken);

// Order management
router.get('/', orderController.getUserOrders);
router.post('/', orderController.createOrder);
router.get('/statistics', orderController.getOrderStatistics);
router.get('/:orderId', orderController.getOrderDetails);
router.put('/:orderId/status', orderController.updateOrderStatus);
router.post('/:orderId/confirm-delivery', orderController.confirmDelivery);

module.exports = router;
