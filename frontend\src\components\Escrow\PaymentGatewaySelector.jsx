import React, { useState, useEffect } from 'react';
import { CreditCard, Shield, Clock, DollarSign } from 'lucide-react';

const PaymentGatewaySelector = ({
  amount,
  currency = 'USD',
  onGatewaySelect,
  selectedGateway,
  className = ''
}) => {
  const [gateways, setGateways] = useState([]);
  const [loading, setLoading] = useState(true);
  const [feeComparison, setFeeComparison] = useState({});

  // Available payment gateways
  useEffect(() => {
    const availableGateways = [
      {
        id: 'paytabs',
        name: 'PayTabs',
        displayName: 'PayTabs',
        logo: '/images/paytabs-logo.png',
        description: 'Secure payment processing for the Middle East',
        supportedMethods: ['credit_card', 'debit_card', 'apple_pay'],
        processingTime: '1-2 business days',
        feePercentage: 2.9,
        fixedFee: 0,
        isRecommended: true,
        features: ['3D Secure', 'Fraud Protection', 'Multi-currency'],
        countries: ['UAE', 'Saudi Arabia', 'Egypt'],
        primaryCurrency: 'SAR',
        currencyNote: currency !== 'SAR' ? `Amount will be converted to SAR` : null,
        statusNote: 'PayTabs servers may be experiencing issues. Try Stripe or PayPal if payment fails.'
      },
      {
        id: 'stripe',
        name: 'Stripe',
        displayName: 'Stripe',
        logo: '/images/stripe-logo.png',
        description: 'Global payment platform trusted worldwide',
        supportedMethods: ['credit_card', 'debit_card', 'apple_pay', 'google_pay'],
        processingTime: '2-7 business days',
        feePercentage: 2.9,
        fixedFee: 0.5, // USD
        isRecommended: false,
        features: ['Advanced Security', 'Global Coverage', 'Developer Friendly'],
        countries: ['Global'],
        primaryCurrency: 'USD'
      },
      {
        id: 'paypal',
        name: 'PayPal',
        displayName: 'PayPal',
        logo: '/images/paypal-logo.png',
        description: 'Pay with your PayPal account or credit card',
        supportedMethods: ['paypal', 'credit_card'],
        processingTime: '1-3 business days',
        feePercentage: 3.4,
        fixedFee: 0,
        isRecommended: false,
        features: ['Buyer Protection', 'Easy Checkout', 'Mobile Optimized'],
        countries: ['Global'],
        primaryCurrency: 'USD'
      }
    ];

    // Calculate fees for each gateway
    const fees = {};
    availableGateways.forEach(gateway => {
      const fee = (amount * gateway.feePercentage / 100) + gateway.fixedFee;
      fees[gateway.id] = {
        fee: Math.round(fee * 100) / 100,
        percentage: gateway.feePercentage,
        fixed: gateway.fixedFee
      };
    });
    console.log('Fees====================:', fees);
    setGateways(availableGateways);
    setFeeComparison(fees);
    setLoading(false);
  }, [amount]);

  const handleGatewaySelect = (gateway) => {
    onGatewaySelect(gateway);
  };

  if (loading) {
    return (
      <div className="animate-pulse space-y-4">
        {[1, 2, 3].map(i => (
          <div key={i} className="h-24 bg-gray-200 rounded-lg"></div>
        ))}
      </div>
    );
  }

  return (
    <div className={`space-y-4 ${className}`}>
      <div className="mb-6">
        <h3 className="text-lg font-semibold text-gray-900 mb-2">
          Choose Payment Method
        </h3>
        <p className="text-sm text-gray-600">
          Select your preferred payment gateway.
        </p>
      </div>

      <div className="grid gap-4">
        {gateways.map((gateway) => {
          const isSelected = selectedGateway?.id === gateway.id;
          const gatewayFee = feeComparison[gateway.id];

          return (
            <div
              key={gateway.id}
              className={`relative border-2 rounded-lg p-4 cursor-pointer transition-all duration-200 ${
                isSelected
                  ? 'border-teal-500 bg-teal-50'
                  : 'border-gray-200 hover:border-gray-300 bg-white'
              }`}
              onClick={() => handleGatewaySelect(gateway)}
            >
              {gateway.isRecommended && (
                <div className="absolute -top-2 left-4">
                  <span className="bg-teal-500 text-white text-xs px-2 py-1 rounded-full">
                    Recommended
                  </span>
                </div>
              )}

              <div className="flex items-start justify-between">
                <div className="flex items-center space-x-4 flex-1">
                  {/* Gateway Logo */}
                  <div className="w-12 h-12 bg-gray-100 rounded-lg flex items-center justify-center">
                    <CreditCard className="w-6 h-6 text-gray-600" />
                  </div>

                  {/* Gateway Info */}
                  <div className="flex-1">
                    <div className="flex items-center space-x-2 mb-1">
                      <h4 className="font-semibold text-gray-900">
                        {gateway.displayName}
                      </h4>
                      <Shield className="w-4 h-4 text-green-500" />
                    </div>
                    
                    <p className="text-sm text-gray-600 mb-2">
                      {gateway.description}
                    </p>

                    {/* Supported Payment Methods */}
                    {/* <div className="flex flex-wrap gap-1 mb-2">
                      {gateway.supportedMethods.map((method) => (
                        <span
                          key={method}
                          className="text-xs bg-gray-100 text-gray-700 px-2 py-1 rounded"
                        >
                          {method.replace('_', ' ').toUpperCase()}
                        </span>
                      ))}
                    </div> */}

                    {/* Features */}
                    <div className="flex items-center space-x-4 text-xs text-gray-500">
                      <div className="flex items-center space-x-1">
                        <Clock className="w-3 h-3" />
                        <span>{gateway.processingTime}</span>
                      </div>
                      <div className="flex items-center space-x-1">
                        <DollarSign className="w-3 h-3" />
                        <span>
                          {gatewayFee?.percentage}% + {currency} {gatewayFee?.fixed}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Fee Display */}
                <div className="text-right pr-8">
                  <div className="text-sm font-semibold text-gray-900">
                    {currency} {gatewayFee?.fee}
                  </div>
                  <div className="text-xs text-gray-500">
                    Processing Fee
                  </div>
                </div>
              </div>

              {/* Expanded Details */}
              {isSelected && (
                <div className="mt-4 pt-4 border-t border-gray-200">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div>
                      <h5 className="text-sm font-medium text-gray-900 mb-2">
                        Key Features
                      </h5>
                      <ul className="text-xs text-gray-600 space-y-1">
                        {gateway.features.map((feature, index) => (
                          <li key={index} className="flex items-center space-x-1">
                            <div className="w-1 h-1 bg-teal-500 rounded-full"></div>
                            <span>{feature}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                    
                    <div>
                      <h5 className="text-sm font-medium text-gray-900 mb-2">
                        Coverage
                      </h5>
                      <div className="text-xs text-gray-600">
                        {gateway.countries.join(', ')}
                      </div>
                    </div>
                  </div>

                  {/* Currency Note */}
                  {gateway.currencyNote && (
                    <div className="mt-3 p-2 bg-amber-50 border border-amber-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 text-amber-600">💱</div>
                        <div className="text-xs text-amber-700">
                          {gateway.currencyNote}
                        </div>
                      </div>
                    </div>
                  )}

                  {/* Status Note */}
                  {gateway.statusNote && (
                    <div className="mt-3 p-2 bg-orange-50 border border-orange-200 rounded-lg">
                      <div className="flex items-center space-x-2">
                        <div className="w-4 h-4 text-orange-600">⚠️</div>
                        <div className="text-xs text-orange-700">
                          {gateway.statusNote}
                        </div>
                      </div>
                    </div>
                  )}


                </div>
              )}

              {/* Selection Indicator */}
              {isSelected && (
                <div className="absolute top-4 right-4">
                  <div className="w-5 h-5 bg-teal-500 rounded-full flex items-center justify-center">
                    <div className="w-2 h-2 bg-white rounded-full"></div>
                  </div>
                </div>
              )}
            </div>
          );
        })}
      </div>

      {/* Fee Summary */}
      {/* {selectedGateway && (
        <div className="mt-6 p-4 bg-gray-50 rounded-lg">
          <h4 className="font-medium text-gray-900 mb-3">Payment Summary</h4>
          <div className="space-y-2 text-sm">
            <div className="flex justify-between">
              <span className="text-gray-600">Product Amount:</span>
              <span className="font-medium">{currency} {amount}</span>
            </div>
            <div className="flex justify-between">
              <span className="text-gray-600">Processing Fee:</span>
              <span className="font-medium">
                {currency} {feeComparison[selectedGateway.id]?.fee}
              </span>
            </div>
            <div className="flex justify-between pt-2 border-t border-gray-200">
              <span className="font-semibold text-gray-900">Total to Pay:</span>
              <span className="font-semibold text-gray-900">
                {currency} {(amount + feeComparison[selectedGateway.id]?.fee).toFixed(2)}
              </span>
            </div>
          </div>
        </div>
      )} */}
    </div>
  );
};

export default PaymentGatewaySelector;
