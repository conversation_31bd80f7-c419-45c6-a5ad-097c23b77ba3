# SOUQ API

A RESTful API for the SOUQ e-commerce platform built with **Node.js** and **MongoDB**.

---

## Project Overview

SOUQ is an e-commerce platform where users can sign up, log in, browse products, follow other users, buy/sell items, and write reviews. This API supports all backend functionalities using Node.js with Express and MongoDB (Mongoose).

---

## Features

- User signup, login (including JWT-based authentication)
- Product management (create, read, update, delete)
- User profiles with follow/unfollow feature
- User-to-user transactions
- Reviews and ratings on products
- Access and Refresh token for secure authentication

---

## Tech Stack

- Node.js
- Express.js
- MongoDB with Mongoose
- JWT for authentication
- bcrypt for password hashing

---

## Installation

- npm install

## Start the development server

- npm run dev