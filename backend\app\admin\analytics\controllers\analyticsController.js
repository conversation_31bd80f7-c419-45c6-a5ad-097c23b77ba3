const User = require('../../../../db/models/userModel');
const Product = require('../../../../db/models/productModel');
const Order = require('../../../../db/models/orderModel');
const Transaction = require('../../../../db/models/transactionModel');
const StandardPayment = require('../../../../db/models/standardPaymentModel');
const { successResponse, errorResponse } = require('../../../../utils/responseHandler');

// Get dashboard overview statistics
exports.getDashboardStats = async (req, res) => {
  try {
    const [
      totalUsers,
      activeUsers,
      totalProducts,
      activeProducts,
      totalOrders,
      completedOrders,
      totalRevenue,
      monthlyRevenue
    ] = await Promise.all([
      User.countDocuments(),
      User.countDocuments({ deletedAt: null }),
      Product.countDocuments(),
      Product.countDocuments({ status: 'active' }),
      Order.countDocuments(),
      Order.countDocuments({ status: 'completed' }),
      Transaction.aggregate([
        { $match: { status: 'completed' } },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ]),
      Transaction.aggregate([
        {
          $match: {
            status: 'completed',
            createdAt: {
              $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
            }
          }
        },
        { $group: { _id: null, total: { $sum: '$amount' } } }
      ])
    ]);

    return successResponse(res, 'Dashboard statistics retrieved successfully', {
      stats: {
        users: {
          total: totalUsers,
          active: activeUsers,
          growth: await calculateGrowthRate('User')
        },
        products: {
          total: totalProducts,
          active: activeProducts,
          growth: await calculateGrowthRate('Product')
        },
        orders: {
          total: totalOrders,
          completed: completedOrders,
          growth: await calculateGrowthRate('Order')
        },
        revenue: {
          total: totalRevenue[0]?.total || 0,
          monthly: monthlyRevenue[0]?.total || 0,
          growth: await calculateRevenueGrowth()
        }
      }
    });

  } catch (error) {
    console.error('Get dashboard stats error:', error);
    return errorResponse(res, 'Internal server error', 500);
  }
};

// Get sales analytics
exports.getSalesAnalytics = async (req, res) => {
  try {
    const { period = '30d', startDate, endDate } = req.query;

    let dateFilter = {};
    if (startDate && endDate) {
      dateFilter = {
        createdAt: {
          $gte: new Date(startDate),
          $lte: new Date(endDate)
        }
      };
    } else {
      const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365;
      dateFilter = {
        createdAt: {
          $gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
        }
      };
    }

    const [
      salesOverTime,
      salesByCategory,
      topSellingProducts,
      paymentMethods,
      orderStatuses
    ] = await Promise.all([
      // Sales over time
      Transaction.aggregate([
        { $match: { status: 'completed', ...dateFilter } },
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' },
              day: { $dayOfMonth: '$createdAt' }
            },
            sales: { $sum: '$amount' },
            orders: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1, '_id.day': 1 } }
      ]),

      // Sales by category
      Order.aggregate([
        { $match: { status: 'completed', ...dateFilter } },
        { $lookup: { from: 'products', localField: 'productId', foreignField: '_id', as: 'product' } },
        { $unwind: '$product' },
        { $lookup: { from: 'categories', localField: 'product.categoryId', foreignField: '_id', as: 'category' } },
        { $unwind: '$category' },
        {
          $group: {
            _id: '$category.name',
            sales: { $sum: '$totalAmount' },
            orders: { $sum: 1 }
          }
        },
        { $sort: { sales: -1 } }
      ]),

      // Top selling products
      Order.aggregate([
        { $match: { status: 'completed', ...dateFilter } },
        { $lookup: { from: 'products', localField: 'productId', foreignField: '_id', as: 'product' } },
        { $unwind: '$product' },
        {
          $group: {
            _id: '$productId',
            title: { $first: '$product.title' },
            sales: { $sum: '$totalAmount' },
            orders: { $sum: 1 }
          }
        },
        { $sort: { sales: -1 } },
        { $limit: 10 }
      ]),

      // Payment methods
      Transaction.aggregate([
        { $match: { status: 'completed', ...dateFilter } },
        {
          $group: {
            _id: '$paymentMethod',
            count: { $sum: 1 },
            amount: { $sum: '$amount' }
          }
        },
        { $sort: { count: -1 } }
      ]),

      // Order statuses
      Order.aggregate([
        { $match: dateFilter },
        {
          $group: {
            _id: '$status',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ])
    ]);

    return successResponse(res, 'Sales analytics retrieved successfully', {
      analytics: {
        salesOverTime,
        salesByCategory,
        topSellingProducts,
        paymentMethods,
        orderStatuses
      }
    });

  } catch (error) {
    console.error('Get sales analytics error:', error);
    return errorResponse(res, 'Internal server error', 500);
  }
};

// Get top sellers
exports.getTopSellers = async (req, res) => {
  try {
    const { period = '30d', limit = 10 } = req.query;

    const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365;
    const dateFilter = {
      createdAt: {
        $gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
      }
    };

    const topSellers = await Order.aggregate([
      { $match: { status: 'completed', ...dateFilter } },
      { $lookup: { from: 'users', localField: 'sellerId', foreignField: '_id', as: 'seller' } },
      { $unwind: '$seller' },
      {
        $group: {
          _id: '$sellerId',
          sellerName: { $first: { $concat: ['$seller.firstName', ' ', '$seller.lastName'] } },
          sellerEmail: { $first: '$seller.email' },
          sellerProfile: { $first: '$seller.profile' },
          totalSales: { $sum: '$totalAmount' },
          totalOrders: { $sum: 1 },
          avgOrderValue: { $avg: '$totalAmount' }
        }
      },
      { $sort: { totalSales: -1 } },
      { $limit: parseInt(limit) }
    ]);

    return successResponse(res, 'Top sellers retrieved successfully', { topSellers });

  } catch (error) {
    console.error('Get top sellers error:', error);
    return errorResponse(res, 'Internal server error', 500);
  }
};

// Get category trends
exports.getCategoryTrends = async (req, res) => {
  try {
    const { period = '30d' } = req.query;

    const days = period === '7d' ? 7 : period === '30d' ? 30 : period === '90d' ? 90 : 365;
    const dateFilter = {
      createdAt: {
        $gte: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
      }
    };

    const [categoryTrends, categoryGrowth] = await Promise.all([
      // Current period trends
      Order.aggregate([
        { $match: { status: 'completed', ...dateFilter } },
        { $lookup: { from: 'products', localField: 'productId', foreignField: '_id', as: 'product' } },
        { $unwind: '$product' },
        { $lookup: { from: 'categories', localField: 'product.categoryId', foreignField: '_id', as: 'category' } },
        { $unwind: '$category' },
        {
          $group: {
            _id: '$category._id',
            categoryName: { $first: '$category.name' },
            sales: { $sum: '$totalAmount' },
            orders: { $sum: 1 },
            avgOrderValue: { $avg: '$totalAmount' }
          }
        },
        { $sort: { sales: -1 } }
      ]),

      // Previous period for growth calculation
      Order.aggregate([
        {
          $match: {
            status: 'completed',
            createdAt: {
              $gte: new Date(Date.now() - (days * 2) * 24 * 60 * 60 * 1000),
              $lt: new Date(Date.now() - days * 24 * 60 * 60 * 1000)
            }
          }
        },
        { $lookup: { from: 'products', localField: 'productId', foreignField: '_id', as: 'product' } },
        { $unwind: '$product' },
        { $lookup: { from: 'categories', localField: 'product.categoryId', foreignField: '_id', as: 'category' } },
        { $unwind: '$category' },
        {
          $group: {
            _id: '$category._id',
            sales: { $sum: '$totalAmount' },
            orders: { $sum: 1 }
          }
        }
      ])
    ]);

    // Calculate growth rates
    const trendsWithGrowth = categoryTrends.map(current => {
      const previous = categoryGrowth.find(p => p._id.toString() === current._id.toString());
      const salesGrowth = previous ? ((current.sales - previous.sales) / previous.sales * 100) : 0;
      const ordersGrowth = previous ? ((current.orders - previous.orders) / previous.orders * 100) : 0;

      return {
        ...current,
        growth: {
          sales: salesGrowth,
          orders: ordersGrowth
        }
      };
    });

    return successResponse(res, 'Category trends retrieved successfully', { categoryTrends: trendsWithGrowth });

  } catch (error) {
    console.error('Get category trends error:', error);
    return errorResponse(res, 'Internal server error', 500);
  }
};

// Get user analytics
exports.getUserAnalytics = async (req, res) => {
  try {
    const [
      userGrowth,
      usersByCountry,
      userActivity,
      userRetention
    ] = await Promise.all([
      // User growth over time
      User.aggregate([
        {
          $group: {
            _id: {
              year: { $year: '$createdAt' },
              month: { $month: '$createdAt' }
            },
            newUsers: { $sum: 1 }
          }
        },
        { $sort: { '_id.year': 1, '_id.month': 1 } },
        { $limit: 12 }
      ]),

      // Users by country
      User.aggregate([
        { $match: { deletedAt: null } },
        {
          $group: {
            _id: '$country',
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } },
        { $limit: 10 }
      ]),

      // User activity (orders per user)
      User.aggregate([
        { $match: { deletedAt: null } },
        { $lookup: { from: 'orders', localField: '_id', foreignField: 'buyerId', as: 'orders' } },
        {
          $project: {
            orderCount: { $size: '$orders' },
            hasOrders: { $gt: [{ $size: '$orders' }, 0] }
          }
        },
        {
          $group: {
            _id: null,
            totalUsers: { $sum: 1 },
            activeUsers: { $sum: { $cond: ['$hasOrders', 1, 0] } },
            avgOrdersPerUser: { $avg: '$orderCount' }
          }
        }
      ]),

      // User retention (users who made multiple orders)
      User.aggregate([
        { $lookup: { from: 'orders', localField: '_id', foreignField: 'buyerId', as: 'orders' } },
        {
          $project: {
            orderCount: { $size: '$orders' }
          }
        },
        {
          $group: {
            _id: {
              $switch: {
                branches: [
                  { case: { $eq: ['$orderCount', 0] }, then: 'No Orders' },
                  { case: { $eq: ['$orderCount', 1] }, then: 'One Order' },
                  { case: { $lte: ['$orderCount', 5] }, then: '2-5 Orders' },
                  { case: { $lte: ['$orderCount', 10] }, then: '6-10 Orders' }
                ],
                default: '10+ Orders'
              }
            },
            count: { $sum: 1 }
          }
        },
        { $sort: { count: -1 } }
      ])
    ]);

    return successResponse(res, 'User analytics retrieved successfully', {
      analytics: {
        userGrowth,
        usersByCountry,
        userActivity: userActivity[0] || { totalUsers: 0, activeUsers: 0, avgOrdersPerUser: 0 },
        userRetention
      }
    });

  } catch (error) {
    console.error('Get user analytics error:', error);
    return errorResponse(res, 'Internal server error', 500);
  }
};

// Helper function to calculate growth rate
async function calculateGrowthRate(model) {
  try {
    const Model = require(`../../../../db/models/${model.toLowerCase()}Model`);
    const currentMonth = await Model.countDocuments({
      createdAt: {
        $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      }
    });
    
    const previousMonth = await Model.countDocuments({
      createdAt: {
        $gte: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
        $lt: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
      }
    });

    return previousMonth > 0 ? ((currentMonth - previousMonth) / previousMonth * 100) : 0;
  } catch (error) {
    return 0;
  }
}

// Helper function to calculate revenue growth
async function calculateRevenueGrowth() {
  try {
    const currentMonth = await Transaction.aggregate([
      {
        $match: {
          status: 'completed',
          createdAt: {
            $gte: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    const previousMonth = await Transaction.aggregate([
      {
        $match: {
          status: 'completed',
          createdAt: {
            $gte: new Date(new Date().getFullYear(), new Date().getMonth() - 1, 1),
            $lt: new Date(new Date().getFullYear(), new Date().getMonth(), 1)
          }
        }
      },
      { $group: { _id: null, total: { $sum: '$amount' } } }
    ]);

    const current = currentMonth[0]?.total || 0;
    const previous = previousMonth[0]?.total || 0;

    return previous > 0 ? ((current - previous) / previous * 100) : 0;
  } catch (error) {
    return 0;
  }
}
