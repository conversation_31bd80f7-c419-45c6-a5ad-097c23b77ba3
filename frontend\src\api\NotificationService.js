import axiosInstance from './AxiosInstance';

// Base route for notifications
const NOTIFICATION_BASE = '/api/user/notifications';

export const NotificationService = {
  // Get user notifications with pagination and optional filters
  async getNotifications(page = 1, limit = 20, status = null, type = null) {
    try {
      const params = { page, limit };
      if (status) params.status = status;
      if (type) params.type = type;

      const response = await axiosInstance.get(`${NOTIFICATION_BASE}`, { params });
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get unread notifications count
  async getUnreadCount() {
    try {
      const response = await axiosInstance.get(`${NOTIFICATION_BASE}/unread-count`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Mark specific notification as read
  async markAsRead(notificationId) {
    try {
      const response = await axiosInstance.patch(`${NOTIFICATION_BASE}/${notificationId}/read`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Mark all notifications as read
  async markAllAsRead() {
    try {
      const response = await axiosInstance.patch(`${NOTIFICATION_BASE}/mark-all-read`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Delete specific notification
  async deleteNotification(notificationId) {
    try {
      const response = await axiosInstance.delete(`${NOTIFICATION_BASE}/${notificationId}`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Get notification settings
  async getNotificationSettings() {
    try {
      const response = await axiosInstance.get(`${NOTIFICATION_BASE}/settings`);
      return response.data;
    } catch (error) {
      throw error;
    }
  },

  // Update notification settings
  async updateNotificationSettings(settings) {
    try {
      const response = await axiosInstance.put(`${NOTIFICATION_BASE}/settings`, settings);
      return response.data;
    } catch (error) {
      throw error;
    }
  }
};

// Notification type mappings for UI
export const NotificationTypes = {
  ORDER_CONFIRMED: 'order_confirmed',
  ORDER_SHIPPED: 'order_shipped',
  ORDER_DELIVERED: 'order_delivered',
  OFFER_RECEIVED: 'offer_received',
  OFFER_ACCEPTED: 'offer_accepted',
  OFFER_DECLINED: 'offer_declined',
  OFFER_EXPIRED: 'offer_expired',
  NEW_FOLLOWER: 'new_follower',
  NEW_MESSAGE: 'new_message',
  NEW_RATING: 'new_rating',
  PAYMENT_RECEIVED: 'payment_received',
  PAYMENT_COMPLETED: 'payment_completed',
  PRODUCT_LIKED: 'product_liked',
  SYSTEM_ANNOUNCEMENT: 'system_announcement'
};

// Notification icons mapping
export const getNotificationIcon = (type) => {
  const iconMap = {
    [NotificationTypes.ORDER_CONFIRMED]: '📦',
    [NotificationTypes.ORDER_SHIPPED]: '🚚',
    [NotificationTypes.ORDER_DELIVERED]: '✅',
    [NotificationTypes.OFFER_RECEIVED]: '💰',
    [NotificationTypes.OFFER_ACCEPTED]: '✅',
    [NotificationTypes.OFFER_DECLINED]: '❌',
    [NotificationTypes.OFFER_EXPIRED]: '⏰',
    [NotificationTypes.NEW_FOLLOWER]: '👤',
    [NotificationTypes.NEW_MESSAGE]: '💬',
    [NotificationTypes.NEW_RATING]: '⭐',
    [NotificationTypes.PAYMENT_RECEIVED]: '💳',
    [NotificationTypes.PAYMENT_COMPLETED]: '✅',
    [NotificationTypes.PRODUCT_LIKED]: '❤️',
    [NotificationTypes.SYSTEM_ANNOUNCEMENT]: '📢'
  };

  return iconMap[type] || '🔔';
};

// Notification colors mapping
export const getNotificationColor = (type) => {
  const colorMap = {
    [NotificationTypes.ORDER_CONFIRMED]: 'text-blue-600',
    [NotificationTypes.ORDER_SHIPPED]: 'text-purple-600',
    [NotificationTypes.ORDER_DELIVERED]: 'text-green-600',
    [NotificationTypes.OFFER_RECEIVED]: 'text-yellow-600',
    [NotificationTypes.OFFER_ACCEPTED]: 'text-green-600',
    [NotificationTypes.OFFER_DECLINED]: 'text-red-600',
    [NotificationTypes.OFFER_EXPIRED]: 'text-gray-600',
    [NotificationTypes.NEW_FOLLOWER]: 'text-blue-600',
    [NotificationTypes.NEW_MESSAGE]: 'text-teal-600',
    [NotificationTypes.NEW_RATING]: 'text-yellow-600',
    [NotificationTypes.PAYMENT_RECEIVED]: 'text-green-600',
    [NotificationTypes.PAYMENT_COMPLETED]: 'text-green-600',
    [NotificationTypes.PRODUCT_LIKED]: 'text-red-600',
    [NotificationTypes.SYSTEM_ANNOUNCEMENT]: 'text-blue-600'
  };

  return colorMap[type] || 'text-gray-600';
};

// Format time ago
export const formatTimeAgo = (timestamp) => {
  const now = new Date();
  const time = new Date(timestamp);
  const diff = now - time;

  const minutes = Math.floor(diff / 60000);
  const hours = Math.floor(diff / 3600000);
  const days = Math.floor(diff / 86400000);

  if (minutes < 1) return 'Just now';
  if (minutes < 60) return `${minutes}m ago`;
  if (hours < 24) return `${hours}h ago`;
  if (days < 7) return `${days}d ago`;
  return time.toLocaleDateString();
};

export default NotificationService;
