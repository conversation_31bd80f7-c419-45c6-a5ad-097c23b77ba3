import React from 'react';
import { Facebook, Instagram, Twitter } from 'lucide-react';
import Logo from '../common/Logo';
import { useTranslation } from 'react-i18next';

const Footer = () => {
  const { t } = useTranslation();
  return (
    <footer className="bg-white border-t border-gray-100 pt-10 pb-6">
      <div className="container mx-auto px-4">
        <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
          <div>
            <Logo />
            <p className="mt-4 text-gray-600 text-sm">
              {t('footer_description')}
            </p>
            <div className="flex space-x-4 rtl:space-x-reverse mt-6">
              <a href="#" className="text-gray-500 hover:text-teal-500"><Facebook size={20} /></a>
              <a href="#" className="text-gray-500 hover:text-teal-500"><Instagram size={20} /></a>
              <a href="#" className="text-gray-500 hover:text-teal-500"><Twitter size={20} /></a>
            </div>
          </div>

          <div>
            <h3 className="font-semibold text-gray-800 mb-4">{t('company')}</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('about')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('careers')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('press')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('sustainability')}</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-gray-800 mb-4">{t('support')}</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('help_center')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('safety_center')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('community_guidelines')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('contact_us')}</a></li>
            </ul>
          </div>

          <div>
            <h3 className="font-semibold text-gray-800 mb-4">{t('legal')}</h3>
            <ul className="space-y-2">
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('privacy_policy')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('terms_of_service')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('cookie_policy')}</a></li>
              <li><a href="#" className="text-gray-600 hover:text-teal-500 text-sm">{t('accessibility')}</a></li>
            </ul>
          </div>
        </div>

        <div className="border-t border-gray-100 mt-8 pt-6">
          <p className="text-center text-gray-500 text-xs">
            © {new Date().getFullYear()} SOUQ {t('all_rights_reserved')}
          </p>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
