# Shipped Status Fix

## Problem
1. **Frontend Issue**: "Mark Shipped" button was showing even when order was already shipped
2. **Backend Issue**: API was returning error "Cannot transition from shipped to shipped. Current payment status: processing"

## Root Cause Analysis

### Frontend Issue
The "Mark Shipped" button was only checking if `order.status === 'processing'` but not excluding already shipped orders.

### Backend Issues
1. **Status Mapping Confusion**: The `getUserOrders` API was returning payment status directly as order status, causing confusion between payment status and order status.
2. **Missing Duplicate Transition Check**: The backend wasn't preventing transitions to the same status.
3. **Inconsistent Status Mapping**: Payment status "processing" was being mapped differently in different parts of the code.

## Solution

### 1. Frontend Changes (`souq-frontend/src/pages/Orders.jsx`)

#### Updated Button Visibility Logic
```javascript
// Before
{isSeller && order.status === 'processing' && (
  <button>Mark Shipped</button>
)}

// After  
{isSeller && ['processing', 'paid'].includes(order.status) && order.status !== 'shipped' && (
  <button>Mark Shipped</button>
)}
```

**Benefits:**
- But<PERSON> is hidden when order is already shipped
- <PERSON><PERSON> shows for both 'processing' and 'paid' statuses
- More explicit and readable condition

### 2. Backend Changes (`souq-backend/app/user/shipping/controllers/orderController.js`)

#### A. Added Status Mapping Helper Method
```javascript
mapPaymentStatusToOrderStatus(paymentStatus) {
  const statusMap = {
    'completed': 'paid',
    'paid': 'paid', 
    'pending': 'pending_payment',
    'processing': 'pending_payment', // Payment being processed, order still pending
    'failed': 'cancelled',
    'cancelled': 'cancelled'
  };
  return statusMap[paymentStatus] || 'pending_payment';
}
```

#### B. Updated getUserOrders Method
```javascript
// For transactions
let orderStatus = transaction.orderStatus || this.mapPaymentStatusToOrderStatus(transaction.status);

// For standard payments  
let orderStatus = payment.orderStatus || this.mapPaymentStatusToOrderStatus(payment.status);
```

**Benefits:**
- Consistent status mapping across the application
- Uses `orderStatus` field if available, falls back to mapped payment status
- Clear separation between payment status and order status

#### C. Added Duplicate Status Transition Check
```javascript
// Check if trying to transition to the same status
if (currentStatus === status) {
  console.log('❌ Cannot transition to same status:', { currentStatus, targetStatus: status });
  return res.status(400).json({
    success: false,
    error: `Order is already ${status}. Cannot transition from ${currentStatus} to ${status}.`
  });
}
```

**Benefits:**
- Prevents duplicate status transitions
- Provides clear error message
- Improves API robustness

#### D. Updated Status Mapping in updateOrderStatus
```javascript
// Before
} else if (order.status === 'processing') {
  currentStatus = 'paid'; // Payment is being processed, so order can be processed

// After
} else if (order.status === 'processing') {
  // Payment is being processed - treat as pending_payment for order status
  // but allow special transition to shipped if seller wants to ship
  currentStatus = 'pending_payment';
```

**Benefits:**
- Consistent with the new mapping logic
- Clearer comments explaining the logic
- Works with the special case handling for processing payments

#### E. Enhanced Special Case Handling
```javascript
// Special case: If payment is completed or processing but order status is still pending_payment,
// allow seller to mark as shipped (auto-transition through paid -> shipped)
if (currentStatus === 'pending_payment' && status === 'shipped' && updatedBy === 'seller') {
  if ((isTransaction || isStandardPayment) && (order.status === 'completed' || order.status === 'processing')) {
    console.log('✅ Auto-transitioning completed/processing payment from pending_payment to shipped');
    currentStatus = 'paid'; // Treat as paid for transition validation
  }
}
```

**Benefits:**
- Handles both 'completed' and 'processing' payment statuses
- Allows sellers to ship orders with processing payments
- Maintains backward compatibility

## Status Flow

### Payment Status vs Order Status
- **Payment Status**: Tracks the payment state (`pending`, `processing`, `completed`, `failed`, etc.)
- **Order Status**: Tracks the fulfillment state (`pending_payment`, `paid`, `processing`, `shipped`, `delivered`, etc.)

### Mapping Logic
```
Payment Status -> Order Status
pending        -> pending_payment
processing     -> pending_payment (with special handling for shipping)
completed      -> paid
paid           -> paid
failed         -> cancelled
cancelled      -> cancelled
```

### Valid Order Status Transitions
```
pending_payment -> [paid, cancelled]
paid           -> [processing, shipped, cancelled]
processing     -> [shipped, cancelled]  
shipped        -> [in_transit, delivered]
in_transit     -> [out_for_delivery, delivered]
out_for_delivery -> [delivered]
delivered      -> [returned]
cancelled      -> [] (final state)
returned       -> [] (final state)
refunded       -> [] (final state)
```

## Testing

### Manual Testing Steps:
1. Ensure backend server is running (`npm run dev`)
2. Login as a seller
3. Navigate to Orders page
4. Find an order with `processing` payment status
5. Verify "Mark Shipped" button is visible
6. Click "Mark Shipped" button - should work successfully
7. Verify button disappears after order is marked as shipped
8. Try clicking "Mark Shipped" again on a shipped order - should not be possible

### API Testing:
```bash
# Test duplicate status transition
curl -X PUT "http://localhost:5000/api/user/orders/ORDER_ID/status" \
  -H "Authorization: Bearer YOUR_TOKEN" \
  -H "Content-Type: application/json" \
  -d '{"status": "shipped"}'

# Should return error if already shipped:
# "Order is already shipped. Cannot transition from shipped to shipped."
```

## Files Modified
1. `souq-frontend/src/pages/Orders.jsx` - Updated button visibility logic
2. `souq-backend/app/user/shipping/controllers/orderController.js` - Added status mapping and validation
3. `souq-backend/test-shipped-status-fix.js` - Test script for validation

## Benefits
- ✅ Prevents duplicate status transitions
- ✅ Hides "Mark Shipped" button for already shipped orders
- ✅ Consistent status mapping between payment and order status
- ✅ Better error messages for invalid transitions
- ✅ Maintains backward compatibility
- ✅ Clearer separation of concerns between payment and order status
