.App {
  text-align: left;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Robot<PERSON>', 'Oxygen',
    'Ubuntu', 'Can<PERSON>ell', 'Fira Sans', 'Droid Sans', 'Helvetica Neue',
    sans-serif;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}

/* Typing animation */
.typing-dots {
  animation: typing 1.4s infinite;
}

@keyframes typing {
  0%, 60%, 100% {
    opacity: 0;
  }
  30% {
    opacity: 1;
  }
}

/* Scrollbar styling */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}

/* Button hover effects */
button:hover {
  transform: translateY(-1px);
  transition: all 0.2s ease;
}

button:active {
  transform: translateY(0);
}

/* Chat message animations */
.message-enter {
  opacity: 0;
  transform: translateY(10px);
}

.message-enter-active {
  opacity: 1;
  transform: translateY(0);
  transition: opacity 300ms, transform 300ms;
}

/* Responsive design */
@media (max-width: 768px) {
  .App {
    padding: 10px;
  }

  /* Make chat components mobile-friendly */
  .chat-container {
    height: calc(100vh - 40px) !important;
    margin: 0 !important;
    border-radius: 0 !important;
  }
}

/* Loading spinner */
.loading-spinner {
  border: 2px solid #f3f3f3;
  border-top: 2px solid #007bff;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  animation: spin 1s linear infinite;
  display: inline-block;
  margin-right: 8px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Status indicators */
.status-online {
  color: #28a745;
}

.status-offline {
  color: #6c757d;
}

.status-typing {
  color: #007bff;
}

/* Message status icons */
.message-status {
  font-size: 12px;
  opacity: 0.7;
}

.message-status.seen {
  color: #007bff;
}

.message-status.delivered {
  color: #6c757d;
}

/* Chat input focus */
textarea:focus {
  outline: none;
  border-color: #007bff !important;
  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);
}

/* Smooth transitions */
* {
  transition: all 0.2s ease;
}
