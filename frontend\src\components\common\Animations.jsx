import Box from '@mui/material/Box';
import Typography from '@mui/material/Typography';
import SentimentDissatisfiedIcon from '@mui/icons-material/SentimentDissatisfied';
// Optional if you want animation
// import { Player } from '@lottiefiles/react-lottie-player';
// import emptyBoxAnimation from './empty-box.json'; // Lottie animation

export const EmptyState = () => {
  return (
    <Box className="flex flex-col items-center justify-center text-center py-10">
      {/* Simple Icon */}
      <SentimentDissatisfiedIcon fontSize="large" className="text-teal-500 animate-bounce" />

      {/* Optional Lottie Animation */}
      {/* 
      <Player
        autoplay
        loop
        src={emptyBoxAnimation}
        style={{ height: '150px', width: '150px' }}
      />
      */}

      <Typography variant="h6" className="mt-4 text-gray-800">
        Member's items
      </Typography>
      <Typography variant="body2" className="text-gray-500 mt-1">
        This member isn't selling anything.
      </Typography>
    </Box>
  );
};
