import React, { useEffect, useRef, useState } from "react";
import { FaArrowLeft, FaArrowRight, FaTimes } from "react-icons/fa";
import { MdRadioButtonChecked, MdRadioButtonUnchecked } from "react-icons/md";
import { sizeCategories } from "../../data/sizeCategories";
import { setSize } from "../../redux/slices/FilterSlice";
import { useDispatch } from "react-redux";
import { LuChevronDown, LuChevronUp } from "react-icons/lu";
import { useTranslation } from "react-i18next";

export default function SizeCategory() {
  const { t } = useTranslation();
  const dispatch = useDispatch()
  const [open, setOpen] = useState(false);
  const [step, setStep] = useState(1);
  const [selectedCategory, setSelectedCategory] = useState(null);
  const [selectedSubcategory, setSelectedSubcategory] = useState(null);
  const [selectedSize, setSelectedSize] = useState(null);
  const dropdownRef = useRef(null);

  useEffect(() => {
    const handleClickOutside = (event) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
        setOpen(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const reset = () => {
    setStep(1);
    setSelectedCategory(null);
    setSelectedSubcategory(null);
    setSelectedSize(null);
  };

  return (
    <div className="relative" ref={dropdownRef}>
      <button
        onClick={() => setOpen(!open)}
        className="border px-4 py-2 rounded-full bg-white shadow flex items-center gap-2 hover:ring-2 hover:ring-teal-500 transition-all duration-200"
      >
        {t('size')}
        {open ? (
          <LuChevronUp className="w-5 h-5 text-gray-600" />
        ) : (
          <LuChevronDown className="w-5 h-5 text-gray-600" />
        )}
      </button>

      {open && (
        <div className="absolute z-10 w-80 bg-white shadow-lg rounded mt-2">
          {step === 1 &&
            sizeCategories.map((cat) => (
              <div
                key={cat.id}
                className="flex justify-between p-3 border-b hover:bg-gray-50 cursor-pointer"
                onClick={() => {
                  setSelectedCategory(cat);
                  setStep(2);
                }}
              >
                <span>{cat.name}</span>
                <FaArrowRight className="text-gray-400" />
              </div>
            ))}

          {step === 2 && selectedCategory && (
            <>
              <div className="flex justify-between items-center p-3 border-b bg-gray-100">
                <FaArrowLeft className="cursor-pointer" onClick={reset} />
                <span className="flex-1 text-center font-medium">{selectedCategory.name}</span>
                <span className="w-5" />
              </div>

              {selectedCategory.subcategories.map((sub) => (
                <div
                  key={sub.id}
                  className="flex justify-between p-3 border-b hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    setSelectedSubcategory(sub);
                    setStep(3);
                  }}
                >
                  <span>{sub.name}</span>
                  <FaArrowRight className="text-gray-400" />
                </div>
              ))}
            </>
          )}

          {step === 3 && selectedSubcategory && (
            <>
              <div className="flex justify-between items-center p-3 border-b bg-gray-100">
                <FaArrowLeft className="cursor-pointer" onClick={() => setStep(2)} />
                <span className="flex-1 text-center font-medium">{selectedSubcategory.name}</span>
                <span className="w-5" />
              </div>

              {selectedSubcategory.sizes.map((size) => (
                <div
                  key={size}
                  className="flex justify-between p-3 border-b hover:bg-gray-50 cursor-pointer"
                  onClick={() => {
                    setSelectedSize(size);
                    dispatch(setSize(size));
                    setOpen(false);
                  }}
                >
                  <span>{size}</span>
                  {selectedSize === size ? (
                    <MdRadioButtonChecked className="text-teal-600" />
                  ) : (
                    <MdRadioButtonUnchecked className="text-teal-600" />
                  )}
                </div>
              ))}
            </>
          )}
        </div>
      )}

      {/* {selectedSize && (
        <button className="border px-4 py-2 rounded-full bg-white shadow mt-3 flex items-center min-w-[100px]">
          <span>{selectedSize}</span>
          <FaTimes className="ml-2 cursor-pointer text-gray-600" onClick={reset} size={16} />
        </button>
      )} */}
    </div>
  );
}
