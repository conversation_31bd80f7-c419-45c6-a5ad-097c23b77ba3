const express = require('express');
const router = express.Router();

// Existing routes
const categorynsRoutes = require('../admin/category/routes/categoryRoutes');
const sizeRoutes = require('../admin/sizeChart/routes/sizeRoutes');
const adminEscrowRoutes = require('./escrow/routes/adminEscrowRoutes');
const menuRoutes = require('./menu/routes/menuRoutes');

// New admin routes
const adminAuthRoutes = require('./auth/routes/adminAuthRoutes');
const userManagementRoutes = require('./users/routes/userManagementRoutes');
const listingManagementRoutes = require('./listings/routes/listingManagementRoutes');
const disputeManagementRoutes = require('./disputes/routes/disputeManagementRoutes');
const counterfeitManagementRoutes = require('./counterfeit/routes/counterfeitManagementRoutes');
const analyticsRoutes = require('./analytics/routes/analyticsRoutes');
const locationRoutes = require('./location/routes/locationRoutes');
const shippingRoutes = require('./shipping/routes/shippingManagementRoutes');
const notificationRoutes = require('./notifications/routes/adminNotificationRoutes');

// Admin Authentication Routes
router.use('/auth', adminAuthRoutes);

// Admin User Management Routes
router.use('/users', userManagementRoutes);

// Admin Listing Management Routes
router.use('/listings', listingManagementRoutes);

// Admin Dispute Management Routes
router.use('/disputes', disputeManagementRoutes);

// Admin Counterfeit Management Routes
router.use('/counterfeit', counterfeitManagementRoutes);

// Admin Analytics Routes
router.use('/analytics', analyticsRoutes);

// Admin Location Management Routes
router.use('/locations', locationRoutes);

// Admin Shipping Management Routes
router.use('/shipping', shippingRoutes);

// Admin Notification Management Routes
router.use('/notifications', notificationRoutes);

// Existing Admin Routes
router.use('/categories', categorynsRoutes);
router.use('/menus', menuRoutes);
router.use('/size', sizeRoutes);
router.use('/escrow', adminEscrowRoutes);

module.exports = router;

