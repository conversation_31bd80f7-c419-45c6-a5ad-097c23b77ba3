# First Message Feature - Chat System

## Overview
When a user clicks "message seller" for the first time, the chat system automatically creates an initial welcome message from the seller. This provides a better user experience by immediately showing seller information and context.

## How It Works

### Backend Implementation
1. **Chat Creation Process** (`chatController.js`):
   - When `createOrGetChat` is called for a new chat (not existing)
   - The system automatically creates a welcome message from the seller
   - The message includes seller's name, location, and last seen information

2. **Welcome Message Format**:
   ```
   Hi, I'm [Seller Name]
   
   No reviews yet
   
   📍 [Country], [City]
   👁 Last seen [time] ago
   ```

3. **Dynamic Information**:
   - **Seller Name**: Uses `firstName lastName` if available, otherwise `userName`
   - **Location**: Shows `country, city` if available, otherwise "Location not specified"
   - **Last Seen**: Calculates time difference from `lastLoginAt`:
     - Minutes ago (< 1 hour)
     - Hours ago (< 24 hours)  
     - Days ago (≥ 24 hours)
     - "Recently" if no lastLoginAt data

### Key Features
- ✅ Only creates welcome message for NEW chats (not existing ones)
- ✅ Uses real seller data (name, location, last login)
- ✅ Dynamic time calculation for "last seen"
- ✅ Fallback values for missing data
- ✅ Message is properly stored in database
- ✅ Updates chat's lastMessage reference

### Database Changes
- No schema changes required
- Uses existing Message model with `messageType: 'text'`
- Message is sent from seller to buyer automatically

### API Response
When creating a new chat, the response includes:
- `isExisting: false` (indicates new chat)
- Chat details with the initial message as `lastMessage`

## Usage Example

### Frontend Integration
```javascript
// When user clicks "message seller" button
const response = await fetch(`/api/user/chat/product/${productId}`, {
  method: 'POST',
  headers: { Authorization: `Bearer ${token}` }
});

const { chat } = response.data;

if (!chat.isExisting) {
  // This is a new chat - welcome message was automatically created
  console.log('New chat created with welcome message');
} else {
  // Existing chat - redirect to existing conversation
  console.log('Redirecting to existing chat');
}
```

### WebSocket Integration
The initial message will appear in the chat interface like any other message, showing:
- Seller's profile picture
- Seller's name
- Welcome message content
- Timestamp

## Benefits
1. **Better UX**: Immediate context when starting a chat
2. **Seller Information**: Buyer sees seller details upfront
3. **No Duplicate Chats**: Prevents multiple chats for same product
4. **Professional Feel**: Similar to marketplace apps like Mercari, OfferUp

## Future Enhancements
- Add seller rating/review count when review system is implemented
- Customize welcome message based on product category
- Allow sellers to set custom welcome messages
- Add product information in the welcome message
